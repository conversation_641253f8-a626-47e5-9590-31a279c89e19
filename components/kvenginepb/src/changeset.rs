// This file is generated by rust-protobuf 2.8.0. Do not edit
// @generated

// https://github.com/Manishearth/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![cfg_attr(rustfmt, rustfmt_skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unsafe_code)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `changeset.proto`

use protobuf::Message as Message_imported_for_functions;
use protobuf::ProtobufEnum as ProtobufEnum_imported_for_functions;

/// Generated files are compatible only with the same version
/// of protobuf runtime.
const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_8_0;

#[derive(<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,Default)]
pub struct ChangeSet {
    // message fields
    pub shard_id: u64,
    pub shard_ver: u64,
    pub compaction: ::protobuf::SingularPtrField<Compaction>,
    pub flush: ::protobuf::SingularPtrField<Flush>,
    pub snapshot: ::protobuf::SingularPtrField<Snapshot>,
    pub initial_flush: ::protobuf::SingularPtrField<Snapshot>,
    pub split: ::protobuf::SingularPtrField<Split>,
    pub shard_delete: bool,
    pub sequence: u64,
    pub parent: ::protobuf::SingularPtrField<ChangeSet>,
    pub ingest_files: ::protobuf::SingularPtrField<IngestFiles>,
    pub property_key: ::std::string::String,
    pub property_value: ::std::vec::Vec<u8>,
    pub property_merge: bool,
    pub destroy_range: ::protobuf::SingularPtrField<TableChange>,
    pub truncate_ts: ::protobuf::SingularPtrField<TableChange>,
    pub trim_over_bound: ::protobuf::SingularPtrField<TableChange>,
    pub restore_shard: ::protobuf::SingularPtrField<Snapshot>,
    pub major_compaction: ::protobuf::SingularPtrField<MajorCompaction>,
    pub update_schema_meta: ::protobuf::SingularPtrField<SchemaMeta>,
    pub columnar_compaction: ::protobuf::SingularPtrField<ColumnarCompaction>,
    pub update_vector_index: ::protobuf::SingularPtrField<UpdateVectorIndex>,
    pub clear_columnar: bool,
    pub snapshot_diff: ::protobuf::SingularPtrField<Snapshot>,
    pub fts_l0_create: ::protobuf::SingularPtrField<FtsL0Create>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ChangeSet {
    fn default() -> &'a ChangeSet {
        <ChangeSet as ::protobuf::Message>::default_instance()
    }
}

impl ChangeSet {
    pub fn new() -> ChangeSet {
        ::std::default::Default::default()
    }

    // uint64 shardID = 1;


    pub fn get_shard_id(&self) -> u64 {
        self.shard_id
    }
    pub fn clear_shard_id(&mut self) {
        self.shard_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_shard_id(&mut self, v: u64) {
        self.shard_id = v;
    }

    // uint64 shardVer = 2;


    pub fn get_shard_ver(&self) -> u64 {
        self.shard_ver
    }
    pub fn clear_shard_ver(&mut self) {
        self.shard_ver = 0;
    }

    // Param is passed by value, moved
    pub fn set_shard_ver(&mut self, v: u64) {
        self.shard_ver = v;
    }

    // .enginepb.Compaction compaction = 4;


    pub fn get_compaction(&self) -> &Compaction {
        self.compaction.as_ref().unwrap_or_else(|| Compaction::default_instance())
    }
    pub fn clear_compaction(&mut self) {
        self.compaction.clear();
    }

    pub fn has_compaction(&self) -> bool {
        self.compaction.is_some()
    }

    // Param is passed by value, moved
    pub fn set_compaction(&mut self, v: Compaction) {
        self.compaction = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_compaction(&mut self) -> &mut Compaction {
        if self.compaction.is_none() {
            self.compaction.set_default();
        }
        self.compaction.as_mut().unwrap()
    }

    // Take field
    pub fn take_compaction(&mut self) -> Compaction {
        self.compaction.take().unwrap_or_else(|| Compaction::new())
    }

    // .enginepb.Flush flush = 5;


    pub fn get_flush(&self) -> &Flush {
        self.flush.as_ref().unwrap_or_else(|| Flush::default_instance())
    }
    pub fn clear_flush(&mut self) {
        self.flush.clear();
    }

    pub fn has_flush(&self) -> bool {
        self.flush.is_some()
    }

    // Param is passed by value, moved
    pub fn set_flush(&mut self, v: Flush) {
        self.flush = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_flush(&mut self) -> &mut Flush {
        if self.flush.is_none() {
            self.flush.set_default();
        }
        self.flush.as_mut().unwrap()
    }

    // Take field
    pub fn take_flush(&mut self) -> Flush {
        self.flush.take().unwrap_or_else(|| Flush::new())
    }

    // .enginepb.Snapshot snapshot = 6;


    pub fn get_snapshot(&self) -> &Snapshot {
        self.snapshot.as_ref().unwrap_or_else(|| Snapshot::default_instance())
    }
    pub fn clear_snapshot(&mut self) {
        self.snapshot.clear();
    }

    pub fn has_snapshot(&self) -> bool {
        self.snapshot.is_some()
    }

    // Param is passed by value, moved
    pub fn set_snapshot(&mut self, v: Snapshot) {
        self.snapshot = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_snapshot(&mut self) -> &mut Snapshot {
        if self.snapshot.is_none() {
            self.snapshot.set_default();
        }
        self.snapshot.as_mut().unwrap()
    }

    // Take field
    pub fn take_snapshot(&mut self) -> Snapshot {
        self.snapshot.take().unwrap_or_else(|| Snapshot::new())
    }

    // .enginepb.Snapshot initial_flush = 7;


    pub fn get_initial_flush(&self) -> &Snapshot {
        self.initial_flush.as_ref().unwrap_or_else(|| Snapshot::default_instance())
    }
    pub fn clear_initial_flush(&mut self) {
        self.initial_flush.clear();
    }

    pub fn has_initial_flush(&self) -> bool {
        self.initial_flush.is_some()
    }

    // Param is passed by value, moved
    pub fn set_initial_flush(&mut self, v: Snapshot) {
        self.initial_flush = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_initial_flush(&mut self) -> &mut Snapshot {
        if self.initial_flush.is_none() {
            self.initial_flush.set_default();
        }
        self.initial_flush.as_mut().unwrap()
    }

    // Take field
    pub fn take_initial_flush(&mut self) -> Snapshot {
        self.initial_flush.take().unwrap_or_else(|| Snapshot::new())
    }

    // .enginepb.Split split = 10;


    pub fn get_split(&self) -> &Split {
        self.split.as_ref().unwrap_or_else(|| Split::default_instance())
    }
    pub fn clear_split(&mut self) {
        self.split.clear();
    }

    pub fn has_split(&self) -> bool {
        self.split.is_some()
    }

    // Param is passed by value, moved
    pub fn set_split(&mut self, v: Split) {
        self.split = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_split(&mut self) -> &mut Split {
        if self.split.is_none() {
            self.split.set_default();
        }
        self.split.as_mut().unwrap()
    }

    // Take field
    pub fn take_split(&mut self) -> Split {
        self.split.take().unwrap_or_else(|| Split::new())
    }

    // bool shardDelete = 11;


    pub fn get_shard_delete(&self) -> bool {
        self.shard_delete
    }
    pub fn clear_shard_delete(&mut self) {
        self.shard_delete = false;
    }

    // Param is passed by value, moved
    pub fn set_shard_delete(&mut self, v: bool) {
        self.shard_delete = v;
    }

    // uint64 sequence = 12;


    pub fn get_sequence(&self) -> u64 {
        self.sequence
    }
    pub fn clear_sequence(&mut self) {
        self.sequence = 0;
    }

    // Param is passed by value, moved
    pub fn set_sequence(&mut self, v: u64) {
        self.sequence = v;
    }

    // .enginepb.ChangeSet parent = 13;


    pub fn get_parent(&self) -> &ChangeSet {
        self.parent.as_ref().unwrap_or_else(|| ChangeSet::default_instance())
    }
    pub fn clear_parent(&mut self) {
        self.parent.clear();
    }

    pub fn has_parent(&self) -> bool {
        self.parent.is_some()
    }

    // Param is passed by value, moved
    pub fn set_parent(&mut self, v: ChangeSet) {
        self.parent = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_parent(&mut self) -> &mut ChangeSet {
        if self.parent.is_none() {
            self.parent.set_default();
        }
        self.parent.as_mut().unwrap()
    }

    // Take field
    pub fn take_parent(&mut self) -> ChangeSet {
        self.parent.take().unwrap_or_else(|| ChangeSet::new())
    }

    // .enginepb.IngestFiles ingest_files = 14;


    pub fn get_ingest_files(&self) -> &IngestFiles {
        self.ingest_files.as_ref().unwrap_or_else(|| IngestFiles::default_instance())
    }
    pub fn clear_ingest_files(&mut self) {
        self.ingest_files.clear();
    }

    pub fn has_ingest_files(&self) -> bool {
        self.ingest_files.is_some()
    }

    // Param is passed by value, moved
    pub fn set_ingest_files(&mut self, v: IngestFiles) {
        self.ingest_files = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_ingest_files(&mut self) -> &mut IngestFiles {
        if self.ingest_files.is_none() {
            self.ingest_files.set_default();
        }
        self.ingest_files.as_mut().unwrap()
    }

    // Take field
    pub fn take_ingest_files(&mut self) -> IngestFiles {
        self.ingest_files.take().unwrap_or_else(|| IngestFiles::new())
    }

    // string property_key = 15;


    pub fn get_property_key(&self) -> &str {
        &self.property_key
    }
    pub fn clear_property_key(&mut self) {
        self.property_key.clear();
    }

    // Param is passed by value, moved
    pub fn set_property_key(&mut self, v: ::std::string::String) {
        self.property_key = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_property_key(&mut self) -> &mut ::std::string::String {
        &mut self.property_key
    }

    // Take field
    pub fn take_property_key(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.property_key, ::std::string::String::new())
    }

    // bytes property_value = 16;


    pub fn get_property_value(&self) -> &[u8] {
        &self.property_value
    }
    pub fn clear_property_value(&mut self) {
        self.property_value.clear();
    }

    // Param is passed by value, moved
    pub fn set_property_value(&mut self, v: ::std::vec::Vec<u8>) {
        self.property_value = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_property_value(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.property_value
    }

    // Take field
    pub fn take_property_value(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.property_value, ::std::vec::Vec::new())
    }

    // bool property_merge = 17;


    pub fn get_property_merge(&self) -> bool {
        self.property_merge
    }
    pub fn clear_property_merge(&mut self) {
        self.property_merge = false;
    }

    // Param is passed by value, moved
    pub fn set_property_merge(&mut self, v: bool) {
        self.property_merge = v;
    }

    // .enginepb.TableChange destroy_range = 18;


    pub fn get_destroy_range(&self) -> &TableChange {
        self.destroy_range.as_ref().unwrap_or_else(|| TableChange::default_instance())
    }
    pub fn clear_destroy_range(&mut self) {
        self.destroy_range.clear();
    }

    pub fn has_destroy_range(&self) -> bool {
        self.destroy_range.is_some()
    }

    // Param is passed by value, moved
    pub fn set_destroy_range(&mut self, v: TableChange) {
        self.destroy_range = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_destroy_range(&mut self) -> &mut TableChange {
        if self.destroy_range.is_none() {
            self.destroy_range.set_default();
        }
        self.destroy_range.as_mut().unwrap()
    }

    // Take field
    pub fn take_destroy_range(&mut self) -> TableChange {
        self.destroy_range.take().unwrap_or_else(|| TableChange::new())
    }

    // .enginepb.TableChange truncate_ts = 19;


    pub fn get_truncate_ts(&self) -> &TableChange {
        self.truncate_ts.as_ref().unwrap_or_else(|| TableChange::default_instance())
    }
    pub fn clear_truncate_ts(&mut self) {
        self.truncate_ts.clear();
    }

    pub fn has_truncate_ts(&self) -> bool {
        self.truncate_ts.is_some()
    }

    // Param is passed by value, moved
    pub fn set_truncate_ts(&mut self, v: TableChange) {
        self.truncate_ts = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_truncate_ts(&mut self) -> &mut TableChange {
        if self.truncate_ts.is_none() {
            self.truncate_ts.set_default();
        }
        self.truncate_ts.as_mut().unwrap()
    }

    // Take field
    pub fn take_truncate_ts(&mut self) -> TableChange {
        self.truncate_ts.take().unwrap_or_else(|| TableChange::new())
    }

    // .enginepb.TableChange trim_over_bound = 20;


    pub fn get_trim_over_bound(&self) -> &TableChange {
        self.trim_over_bound.as_ref().unwrap_or_else(|| TableChange::default_instance())
    }
    pub fn clear_trim_over_bound(&mut self) {
        self.trim_over_bound.clear();
    }

    pub fn has_trim_over_bound(&self) -> bool {
        self.trim_over_bound.is_some()
    }

    // Param is passed by value, moved
    pub fn set_trim_over_bound(&mut self, v: TableChange) {
        self.trim_over_bound = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_trim_over_bound(&mut self) -> &mut TableChange {
        if self.trim_over_bound.is_none() {
            self.trim_over_bound.set_default();
        }
        self.trim_over_bound.as_mut().unwrap()
    }

    // Take field
    pub fn take_trim_over_bound(&mut self) -> TableChange {
        self.trim_over_bound.take().unwrap_or_else(|| TableChange::new())
    }

    // .enginepb.Snapshot restore_shard = 21;


    pub fn get_restore_shard(&self) -> &Snapshot {
        self.restore_shard.as_ref().unwrap_or_else(|| Snapshot::default_instance())
    }
    pub fn clear_restore_shard(&mut self) {
        self.restore_shard.clear();
    }

    pub fn has_restore_shard(&self) -> bool {
        self.restore_shard.is_some()
    }

    // Param is passed by value, moved
    pub fn set_restore_shard(&mut self, v: Snapshot) {
        self.restore_shard = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_restore_shard(&mut self) -> &mut Snapshot {
        if self.restore_shard.is_none() {
            self.restore_shard.set_default();
        }
        self.restore_shard.as_mut().unwrap()
    }

    // Take field
    pub fn take_restore_shard(&mut self) -> Snapshot {
        self.restore_shard.take().unwrap_or_else(|| Snapshot::new())
    }

    // .enginepb.MajorCompaction major_compaction = 22;


    pub fn get_major_compaction(&self) -> &MajorCompaction {
        self.major_compaction.as_ref().unwrap_or_else(|| MajorCompaction::default_instance())
    }
    pub fn clear_major_compaction(&mut self) {
        self.major_compaction.clear();
    }

    pub fn has_major_compaction(&self) -> bool {
        self.major_compaction.is_some()
    }

    // Param is passed by value, moved
    pub fn set_major_compaction(&mut self, v: MajorCompaction) {
        self.major_compaction = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_major_compaction(&mut self) -> &mut MajorCompaction {
        if self.major_compaction.is_none() {
            self.major_compaction.set_default();
        }
        self.major_compaction.as_mut().unwrap()
    }

    // Take field
    pub fn take_major_compaction(&mut self) -> MajorCompaction {
        self.major_compaction.take().unwrap_or_else(|| MajorCompaction::new())
    }

    // .enginepb.SchemaMeta update_schema_meta = 23;


    pub fn get_update_schema_meta(&self) -> &SchemaMeta {
        self.update_schema_meta.as_ref().unwrap_or_else(|| SchemaMeta::default_instance())
    }
    pub fn clear_update_schema_meta(&mut self) {
        self.update_schema_meta.clear();
    }

    pub fn has_update_schema_meta(&self) -> bool {
        self.update_schema_meta.is_some()
    }

    // Param is passed by value, moved
    pub fn set_update_schema_meta(&mut self, v: SchemaMeta) {
        self.update_schema_meta = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_update_schema_meta(&mut self) -> &mut SchemaMeta {
        if self.update_schema_meta.is_none() {
            self.update_schema_meta.set_default();
        }
        self.update_schema_meta.as_mut().unwrap()
    }

    // Take field
    pub fn take_update_schema_meta(&mut self) -> SchemaMeta {
        self.update_schema_meta.take().unwrap_or_else(|| SchemaMeta::new())
    }

    // .enginepb.ColumnarCompaction columnar_compaction = 24;


    pub fn get_columnar_compaction(&self) -> &ColumnarCompaction {
        self.columnar_compaction.as_ref().unwrap_or_else(|| ColumnarCompaction::default_instance())
    }
    pub fn clear_columnar_compaction(&mut self) {
        self.columnar_compaction.clear();
    }

    pub fn has_columnar_compaction(&self) -> bool {
        self.columnar_compaction.is_some()
    }

    // Param is passed by value, moved
    pub fn set_columnar_compaction(&mut self, v: ColumnarCompaction) {
        self.columnar_compaction = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_columnar_compaction(&mut self) -> &mut ColumnarCompaction {
        if self.columnar_compaction.is_none() {
            self.columnar_compaction.set_default();
        }
        self.columnar_compaction.as_mut().unwrap()
    }

    // Take field
    pub fn take_columnar_compaction(&mut self) -> ColumnarCompaction {
        self.columnar_compaction.take().unwrap_or_else(|| ColumnarCompaction::new())
    }

    // .enginepb.UpdateVectorIndex update_vector_index = 25;


    pub fn get_update_vector_index(&self) -> &UpdateVectorIndex {
        self.update_vector_index.as_ref().unwrap_or_else(|| UpdateVectorIndex::default_instance())
    }
    pub fn clear_update_vector_index(&mut self) {
        self.update_vector_index.clear();
    }

    pub fn has_update_vector_index(&self) -> bool {
        self.update_vector_index.is_some()
    }

    // Param is passed by value, moved
    pub fn set_update_vector_index(&mut self, v: UpdateVectorIndex) {
        self.update_vector_index = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_update_vector_index(&mut self) -> &mut UpdateVectorIndex {
        if self.update_vector_index.is_none() {
            self.update_vector_index.set_default();
        }
        self.update_vector_index.as_mut().unwrap()
    }

    // Take field
    pub fn take_update_vector_index(&mut self) -> UpdateVectorIndex {
        self.update_vector_index.take().unwrap_or_else(|| UpdateVectorIndex::new())
    }

    // bool clear_columnar = 26;


    pub fn get_clear_columnar(&self) -> bool {
        self.clear_columnar
    }
    pub fn clear_clear_columnar(&mut self) {
        self.clear_columnar = false;
    }

    // Param is passed by value, moved
    pub fn set_clear_columnar(&mut self, v: bool) {
        self.clear_columnar = v;
    }

    // .enginepb.Snapshot snapshot_diff = 27;


    pub fn get_snapshot_diff(&self) -> &Snapshot {
        self.snapshot_diff.as_ref().unwrap_or_else(|| Snapshot::default_instance())
    }
    pub fn clear_snapshot_diff(&mut self) {
        self.snapshot_diff.clear();
    }

    pub fn has_snapshot_diff(&self) -> bool {
        self.snapshot_diff.is_some()
    }

    // Param is passed by value, moved
    pub fn set_snapshot_diff(&mut self, v: Snapshot) {
        self.snapshot_diff = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_snapshot_diff(&mut self) -> &mut Snapshot {
        if self.snapshot_diff.is_none() {
            self.snapshot_diff.set_default();
        }
        self.snapshot_diff.as_mut().unwrap()
    }

    // Take field
    pub fn take_snapshot_diff(&mut self) -> Snapshot {
        self.snapshot_diff.take().unwrap_or_else(|| Snapshot::new())
    }

    // .enginepb.FtsL0Create fts_l0_create = 28;


    pub fn get_fts_l0_create(&self) -> &FtsL0Create {
        self.fts_l0_create.as_ref().unwrap_or_else(|| FtsL0Create::default_instance())
    }
    pub fn clear_fts_l0_create(&mut self) {
        self.fts_l0_create.clear();
    }

    pub fn has_fts_l0_create(&self) -> bool {
        self.fts_l0_create.is_some()
    }

    // Param is passed by value, moved
    pub fn set_fts_l0_create(&mut self, v: FtsL0Create) {
        self.fts_l0_create = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_fts_l0_create(&mut self) -> &mut FtsL0Create {
        if self.fts_l0_create.is_none() {
            self.fts_l0_create.set_default();
        }
        self.fts_l0_create.as_mut().unwrap()
    }

    // Take field
    pub fn take_fts_l0_create(&mut self) -> FtsL0Create {
        self.fts_l0_create.take().unwrap_or_else(|| FtsL0Create::new())
    }
}

impl ::protobuf::Message for ChangeSet {
    fn is_initialized(&self) -> bool {
        for v in &self.compaction {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.flush {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.snapshot {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.initial_flush {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.split {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.parent {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.ingest_files {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.destroy_range {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.truncate_ts {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.trim_over_bound {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.restore_shard {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.major_compaction {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.update_schema_meta {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.columnar_compaction {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.update_vector_index {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.snapshot_diff {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.fts_l0_create {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.shard_id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.shard_ver = tmp;
                },
                4 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.compaction)?;
                },
                5 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.flush)?;
                },
                6 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.snapshot)?;
                },
                7 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.initial_flush)?;
                },
                10 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.split)?;
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.shard_delete = tmp;
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.sequence = tmp;
                },
                13 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.parent)?;
                },
                14 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.ingest_files)?;
                },
                15 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.property_key)?;
                },
                16 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.property_value)?;
                },
                17 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.property_merge = tmp;
                },
                18 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.destroy_range)?;
                },
                19 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.truncate_ts)?;
                },
                20 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.trim_over_bound)?;
                },
                21 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.restore_shard)?;
                },
                22 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.major_compaction)?;
                },
                23 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.update_schema_meta)?;
                },
                24 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.columnar_compaction)?;
                },
                25 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.update_vector_index)?;
                },
                26 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.clear_columnar = tmp;
                },
                27 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.snapshot_diff)?;
                },
                28 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.fts_l0_create)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.shard_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.shard_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.shard_ver != 0 {
            my_size += ::protobuf::rt::value_size(2, self.shard_ver, ::protobuf::wire_format::WireTypeVarint);
        }
        if let Some(ref v) = self.compaction.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.flush.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.snapshot.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.initial_flush.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.split.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.shard_delete != false {
            my_size += 2;
        }
        if self.sequence != 0 {
            my_size += ::protobuf::rt::value_size(12, self.sequence, ::protobuf::wire_format::WireTypeVarint);
        }
        if let Some(ref v) = self.parent.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.ingest_files.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if !self.property_key.is_empty() {
            my_size += ::protobuf::rt::string_size(15, &self.property_key);
        }
        if !self.property_value.is_empty() {
            my_size += ::protobuf::rt::bytes_size(16, &self.property_value);
        }
        if self.property_merge != false {
            my_size += 3;
        }
        if let Some(ref v) = self.destroy_range.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.truncate_ts.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.trim_over_bound.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.restore_shard.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.major_compaction.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.update_schema_meta.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.columnar_compaction.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.update_vector_index.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.clear_columnar != false {
            my_size += 3;
        }
        if let Some(ref v) = self.snapshot_diff.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.fts_l0_create.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.shard_id != 0 {
            os.write_uint64(1, self.shard_id)?;
        }
        if self.shard_ver != 0 {
            os.write_uint64(2, self.shard_ver)?;
        }
        if let Some(ref v) = self.compaction.as_ref() {
            os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.flush.as_ref() {
            os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.snapshot.as_ref() {
            os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.initial_flush.as_ref() {
            os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.split.as_ref() {
            os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.shard_delete != false {
            os.write_bool(11, self.shard_delete)?;
        }
        if self.sequence != 0 {
            os.write_uint64(12, self.sequence)?;
        }
        if let Some(ref v) = self.parent.as_ref() {
            os.write_tag(13, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.ingest_files.as_ref() {
            os.write_tag(14, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if !self.property_key.is_empty() {
            os.write_string(15, &self.property_key)?;
        }
        if !self.property_value.is_empty() {
            os.write_bytes(16, &self.property_value)?;
        }
        if self.property_merge != false {
            os.write_bool(17, self.property_merge)?;
        }
        if let Some(ref v) = self.destroy_range.as_ref() {
            os.write_tag(18, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.truncate_ts.as_ref() {
            os.write_tag(19, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.trim_over_bound.as_ref() {
            os.write_tag(20, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.restore_shard.as_ref() {
            os.write_tag(21, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.major_compaction.as_ref() {
            os.write_tag(22, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.update_schema_meta.as_ref() {
            os.write_tag(23, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.columnar_compaction.as_ref() {
            os.write_tag(24, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.update_vector_index.as_ref() {
            os.write_tag(25, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.clear_columnar != false {
            os.write_bool(26, self.clear_columnar)?;
        }
        if let Some(ref v) = self.snapshot_diff.as_ref() {
            os.write_tag(27, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.fts_l0_create.as_ref() {
            os.write_tag(28, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ChangeSet {
        ChangeSet::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "shardID",
                    |m: &ChangeSet| { &m.shard_id },
                    |m: &mut ChangeSet| { &mut m.shard_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "shardVer",
                    |m: &ChangeSet| { &m.shard_ver },
                    |m: &mut ChangeSet| { &mut m.shard_ver },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Compaction>>(
                    "compaction",
                    |m: &ChangeSet| { &m.compaction },
                    |m: &mut ChangeSet| { &mut m.compaction },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Flush>>(
                    "flush",
                    |m: &ChangeSet| { &m.flush },
                    |m: &mut ChangeSet| { &mut m.flush },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Snapshot>>(
                    "snapshot",
                    |m: &ChangeSet| { &m.snapshot },
                    |m: &mut ChangeSet| { &mut m.snapshot },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Snapshot>>(
                    "initial_flush",
                    |m: &ChangeSet| { &m.initial_flush },
                    |m: &mut ChangeSet| { &mut m.initial_flush },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Split>>(
                    "split",
                    |m: &ChangeSet| { &m.split },
                    |m: &mut ChangeSet| { &mut m.split },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                    "shardDelete",
                    |m: &ChangeSet| { &m.shard_delete },
                    |m: &mut ChangeSet| { &mut m.shard_delete },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "sequence",
                    |m: &ChangeSet| { &m.sequence },
                    |m: &mut ChangeSet| { &mut m.sequence },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ChangeSet>>(
                    "parent",
                    |m: &ChangeSet| { &m.parent },
                    |m: &mut ChangeSet| { &mut m.parent },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<IngestFiles>>(
                    "ingest_files",
                    |m: &ChangeSet| { &m.ingest_files },
                    |m: &mut ChangeSet| { &mut m.ingest_files },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                    "property_key",
                    |m: &ChangeSet| { &m.property_key },
                    |m: &mut ChangeSet| { &mut m.property_key },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "property_value",
                    |m: &ChangeSet| { &m.property_value },
                    |m: &mut ChangeSet| { &mut m.property_value },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                    "property_merge",
                    |m: &ChangeSet| { &m.property_merge },
                    |m: &mut ChangeSet| { &mut m.property_merge },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableChange>>(
                    "destroy_range",
                    |m: &ChangeSet| { &m.destroy_range },
                    |m: &mut ChangeSet| { &mut m.destroy_range },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableChange>>(
                    "truncate_ts",
                    |m: &ChangeSet| { &m.truncate_ts },
                    |m: &mut ChangeSet| { &mut m.truncate_ts },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableChange>>(
                    "trim_over_bound",
                    |m: &ChangeSet| { &m.trim_over_bound },
                    |m: &mut ChangeSet| { &mut m.trim_over_bound },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Snapshot>>(
                    "restore_shard",
                    |m: &ChangeSet| { &m.restore_shard },
                    |m: &mut ChangeSet| { &mut m.restore_shard },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<MajorCompaction>>(
                    "major_compaction",
                    |m: &ChangeSet| { &m.major_compaction },
                    |m: &mut ChangeSet| { &mut m.major_compaction },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<SchemaMeta>>(
                    "update_schema_meta",
                    |m: &ChangeSet| { &m.update_schema_meta },
                    |m: &mut ChangeSet| { &mut m.update_schema_meta },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ColumnarCompaction>>(
                    "columnar_compaction",
                    |m: &ChangeSet| { &m.columnar_compaction },
                    |m: &mut ChangeSet| { &mut m.columnar_compaction },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<UpdateVectorIndex>>(
                    "update_vector_index",
                    |m: &ChangeSet| { &m.update_vector_index },
                    |m: &mut ChangeSet| { &mut m.update_vector_index },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                    "clear_columnar",
                    |m: &ChangeSet| { &m.clear_columnar },
                    |m: &mut ChangeSet| { &mut m.clear_columnar },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Snapshot>>(
                    "snapshot_diff",
                    |m: &ChangeSet| { &m.snapshot_diff },
                    |m: &mut ChangeSet| { &mut m.snapshot_diff },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FtsL0Create>>(
                    "fts_l0_create",
                    |m: &ChangeSet| { &m.fts_l0_create },
                    |m: &mut ChangeSet| { &mut m.fts_l0_create },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<ChangeSet>(
                    "ChangeSet",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static ChangeSet {
        static mut instance: ::protobuf::lazy::Lazy<ChangeSet> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ChangeSet,
        };
        unsafe {
            instance.get(ChangeSet::new)
        }
    }
}

impl ::protobuf::Clear for ChangeSet {
    fn clear(&mut self) {
        self.shard_id = 0;
        self.shard_ver = 0;
        self.compaction.clear();
        self.flush.clear();
        self.snapshot.clear();
        self.initial_flush.clear();
        self.split.clear();
        self.shard_delete = false;
        self.sequence = 0;
        self.parent.clear();
        self.ingest_files.clear();
        self.property_key.clear();
        self.property_value.clear();
        self.property_merge = false;
        self.destroy_range.clear();
        self.truncate_ts.clear();
        self.trim_over_bound.clear();
        self.restore_shard.clear();
        self.major_compaction.clear();
        self.update_schema_meta.clear();
        self.columnar_compaction.clear();
        self.update_vector_index.clear();
        self.clear_columnar = false;
        self.snapshot_diff.clear();
        self.fts_l0_create.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for ChangeSet {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.shard_id, "shard_id", buf);
        ::protobuf::PbPrint::fmt(&self.shard_ver, "shard_ver", buf);
        ::protobuf::PbPrint::fmt(&self.compaction, "compaction", buf);
        ::protobuf::PbPrint::fmt(&self.flush, "flush", buf);
        ::protobuf::PbPrint::fmt(&self.snapshot, "snapshot", buf);
        ::protobuf::PbPrint::fmt(&self.initial_flush, "initial_flush", buf);
        ::protobuf::PbPrint::fmt(&self.split, "split", buf);
        ::protobuf::PbPrint::fmt(&self.shard_delete, "shard_delete", buf);
        ::protobuf::PbPrint::fmt(&self.sequence, "sequence", buf);
        ::protobuf::PbPrint::fmt(&self.parent, "parent", buf);
        ::protobuf::PbPrint::fmt(&self.ingest_files, "ingest_files", buf);
        ::protobuf::PbPrint::fmt(&self.property_key, "property_key", buf);
        ::protobuf::PbPrint::fmt(&self.property_value, "property_value", buf);
        ::protobuf::PbPrint::fmt(&self.property_merge, "property_merge", buf);
        ::protobuf::PbPrint::fmt(&self.destroy_range, "destroy_range", buf);
        ::protobuf::PbPrint::fmt(&self.truncate_ts, "truncate_ts", buf);
        ::protobuf::PbPrint::fmt(&self.trim_over_bound, "trim_over_bound", buf);
        ::protobuf::PbPrint::fmt(&self.restore_shard, "restore_shard", buf);
        ::protobuf::PbPrint::fmt(&self.major_compaction, "major_compaction", buf);
        ::protobuf::PbPrint::fmt(&self.update_schema_meta, "update_schema_meta", buf);
        ::protobuf::PbPrint::fmt(&self.columnar_compaction, "columnar_compaction", buf);
        ::protobuf::PbPrint::fmt(&self.update_vector_index, "update_vector_index", buf);
        ::protobuf::PbPrint::fmt(&self.clear_columnar, "clear_columnar", buf);
        ::protobuf::PbPrint::fmt(&self.snapshot_diff, "snapshot_diff", buf);
        ::protobuf::PbPrint::fmt(&self.fts_l0_create, "fts_l0_create", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for ChangeSet {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.shard_id, "shard_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.shard_ver, "shard_ver", &mut s);
        ::protobuf::PbPrint::fmt(&self.compaction, "compaction", &mut s);
        ::protobuf::PbPrint::fmt(&self.flush, "flush", &mut s);
        ::protobuf::PbPrint::fmt(&self.snapshot, "snapshot", &mut s);
        ::protobuf::PbPrint::fmt(&self.initial_flush, "initial_flush", &mut s);
        ::protobuf::PbPrint::fmt(&self.split, "split", &mut s);
        ::protobuf::PbPrint::fmt(&self.shard_delete, "shard_delete", &mut s);
        ::protobuf::PbPrint::fmt(&self.sequence, "sequence", &mut s);
        ::protobuf::PbPrint::fmt(&self.parent, "parent", &mut s);
        ::protobuf::PbPrint::fmt(&self.ingest_files, "ingest_files", &mut s);
        ::protobuf::PbPrint::fmt(&self.property_key, "property_key", &mut s);
        ::protobuf::PbPrint::fmt(&self.property_value, "property_value", &mut s);
        ::protobuf::PbPrint::fmt(&self.property_merge, "property_merge", &mut s);
        ::protobuf::PbPrint::fmt(&self.destroy_range, "destroy_range", &mut s);
        ::protobuf::PbPrint::fmt(&self.truncate_ts, "truncate_ts", &mut s);
        ::protobuf::PbPrint::fmt(&self.trim_over_bound, "trim_over_bound", &mut s);
        ::protobuf::PbPrint::fmt(&self.restore_shard, "restore_shard", &mut s);
        ::protobuf::PbPrint::fmt(&self.major_compaction, "major_compaction", &mut s);
        ::protobuf::PbPrint::fmt(&self.update_schema_meta, "update_schema_meta", &mut s);
        ::protobuf::PbPrint::fmt(&self.columnar_compaction, "columnar_compaction", &mut s);
        ::protobuf::PbPrint::fmt(&self.update_vector_index, "update_vector_index", &mut s);
        ::protobuf::PbPrint::fmt(&self.clear_columnar, "clear_columnar", &mut s);
        ::protobuf::PbPrint::fmt(&self.snapshot_diff, "snapshot_diff", &mut s);
        ::protobuf::PbPrint::fmt(&self.fts_l0_create, "fts_l0_create", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for ChangeSet {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ChangeSets {
    // message fields
    pub change_sets: ::protobuf::RepeatedField<ChangeSet>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ChangeSets {
    fn default() -> &'a ChangeSets {
        <ChangeSets as ::protobuf::Message>::default_instance()
    }
}

impl ChangeSets {
    pub fn new() -> ChangeSets {
        ::std::default::Default::default()
    }

    // repeated .enginepb.ChangeSet change_sets = 1;


    pub fn get_change_sets(&self) -> &[ChangeSet] {
        &self.change_sets
    }
    pub fn clear_change_sets(&mut self) {
        self.change_sets.clear();
    }

    // Param is passed by value, moved
    pub fn set_change_sets(&mut self, v: ::protobuf::RepeatedField<ChangeSet>) {
        self.change_sets = v;
    }

    // Mutable pointer to the field.
    pub fn mut_change_sets(&mut self) -> &mut ::protobuf::RepeatedField<ChangeSet> {
        &mut self.change_sets
    }

    // Take field
    pub fn take_change_sets(&mut self) -> ::protobuf::RepeatedField<ChangeSet> {
        ::std::mem::replace(&mut self.change_sets, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for ChangeSets {
    fn is_initialized(&self) -> bool {
        for v in &self.change_sets {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.change_sets)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.change_sets {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        for v in &self.change_sets {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ChangeSets {
        ChangeSets::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ChangeSet>>(
                    "change_sets",
                    |m: &ChangeSets| { &m.change_sets },
                    |m: &mut ChangeSets| { &mut m.change_sets },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<ChangeSets>(
                    "ChangeSets",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static ChangeSets {
        static mut instance: ::protobuf::lazy::Lazy<ChangeSets> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ChangeSets,
        };
        unsafe {
            instance.get(ChangeSets::new)
        }
    }
}

impl ::protobuf::Clear for ChangeSets {
    fn clear(&mut self) {
        self.change_sets.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for ChangeSets {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.change_sets, "change_sets", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for ChangeSets {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.change_sets, "change_sets", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for ChangeSets {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct Compaction {
    // message fields
    pub cf: i32,
    pub level: u32,
    pub table_creates: ::protobuf::RepeatedField<TableCreate>,
    pub top_deletes: ::std::vec::Vec<u64>,
    pub bottom_deletes: ::std::vec::Vec<u64>,
    pub conflicted: bool,
    pub blob_tables: ::protobuf::RepeatedField<BlobCreate>,
    pub fts_l0_creates: ::protobuf::RepeatedField<FtsL0Create>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Compaction {
    fn default() -> &'a Compaction {
        <Compaction as ::protobuf::Message>::default_instance()
    }
}

impl Compaction {
    pub fn new() -> Compaction {
        ::std::default::Default::default()
    }

    // int32 cf = 1;


    pub fn get_cf(&self) -> i32 {
        self.cf
    }
    pub fn clear_cf(&mut self) {
        self.cf = 0;
    }

    // Param is passed by value, moved
    pub fn set_cf(&mut self, v: i32) {
        self.cf = v;
    }

    // uint32 level = 2;


    pub fn get_level(&self) -> u32 {
        self.level
    }
    pub fn clear_level(&mut self) {
        self.level = 0;
    }

    // Param is passed by value, moved
    pub fn set_level(&mut self, v: u32) {
        self.level = v;
    }

    // repeated .enginepb.TableCreate tableCreates = 3;


    pub fn get_table_creates(&self) -> &[TableCreate] {
        &self.table_creates
    }
    pub fn clear_table_creates(&mut self) {
        self.table_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_table_creates(&mut self, v: ::protobuf::RepeatedField<TableCreate>) {
        self.table_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_table_creates(&mut self) -> &mut ::protobuf::RepeatedField<TableCreate> {
        &mut self.table_creates
    }

    // Take field
    pub fn take_table_creates(&mut self) -> ::protobuf::RepeatedField<TableCreate> {
        ::std::mem::replace(&mut self.table_creates, ::protobuf::RepeatedField::new())
    }

    // repeated uint64 topDeletes = 4;


    pub fn get_top_deletes(&self) -> &[u64] {
        &self.top_deletes
    }
    pub fn clear_top_deletes(&mut self) {
        self.top_deletes.clear();
    }

    // Param is passed by value, moved
    pub fn set_top_deletes(&mut self, v: ::std::vec::Vec<u64>) {
        self.top_deletes = v;
    }

    // Mutable pointer to the field.
    pub fn mut_top_deletes(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.top_deletes
    }

    // Take field
    pub fn take_top_deletes(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.top_deletes, ::std::vec::Vec::new())
    }

    // repeated uint64 bottomDeletes = 5;


    pub fn get_bottom_deletes(&self) -> &[u64] {
        &self.bottom_deletes
    }
    pub fn clear_bottom_deletes(&mut self) {
        self.bottom_deletes.clear();
    }

    // Param is passed by value, moved
    pub fn set_bottom_deletes(&mut self, v: ::std::vec::Vec<u64>) {
        self.bottom_deletes = v;
    }

    // Mutable pointer to the field.
    pub fn mut_bottom_deletes(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.bottom_deletes
    }

    // Take field
    pub fn take_bottom_deletes(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.bottom_deletes, ::std::vec::Vec::new())
    }

    // bool conflicted = 6;


    pub fn get_conflicted(&self) -> bool {
        self.conflicted
    }
    pub fn clear_conflicted(&mut self) {
        self.conflicted = false;
    }

    // Param is passed by value, moved
    pub fn set_conflicted(&mut self, v: bool) {
        self.conflicted = v;
    }

    // repeated .enginepb.BlobCreate blobTables = 7;


    pub fn get_blob_tables(&self) -> &[BlobCreate] {
        &self.blob_tables
    }
    pub fn clear_blob_tables(&mut self) {
        self.blob_tables.clear();
    }

    // Param is passed by value, moved
    pub fn set_blob_tables(&mut self, v: ::protobuf::RepeatedField<BlobCreate>) {
        self.blob_tables = v;
    }

    // Mutable pointer to the field.
    pub fn mut_blob_tables(&mut self) -> &mut ::protobuf::RepeatedField<BlobCreate> {
        &mut self.blob_tables
    }

    // Take field
    pub fn take_blob_tables(&mut self) -> ::protobuf::RepeatedField<BlobCreate> {
        ::std::mem::replace(&mut self.blob_tables, ::protobuf::RepeatedField::new())
    }

    // repeated .enginepb.FtsL0Create fts_l0_creates = 8;


    pub fn get_fts_l0_creates(&self) -> &[FtsL0Create] {
        &self.fts_l0_creates
    }
    pub fn clear_fts_l0_creates(&mut self) {
        self.fts_l0_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_fts_l0_creates(&mut self, v: ::protobuf::RepeatedField<FtsL0Create>) {
        self.fts_l0_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_fts_l0_creates(&mut self) -> &mut ::protobuf::RepeatedField<FtsL0Create> {
        &mut self.fts_l0_creates
    }

    // Take field
    pub fn take_fts_l0_creates(&mut self) -> ::protobuf::RepeatedField<FtsL0Create> {
        ::std::mem::replace(&mut self.fts_l0_creates, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for Compaction {
    fn is_initialized(&self) -> bool {
        for v in &self.table_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.blob_tables {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.fts_l0_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.cf = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.level = tmp;
                },
                3 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.table_creates)?;
                },
                4 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.top_deletes)?;
                },
                5 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.bottom_deletes)?;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.conflicted = tmp;
                },
                7 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.blob_tables)?;
                },
                8 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.fts_l0_creates)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.cf != 0 {
            my_size += ::protobuf::rt::value_size(1, self.cf, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.level != 0 {
            my_size += ::protobuf::rt::value_size(2, self.level, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.table_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.top_deletes {
            my_size += ::protobuf::rt::value_size(4, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        for value in &self.bottom_deletes {
            my_size += ::protobuf::rt::value_size(5, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        if self.conflicted != false {
            my_size += 2;
        }
        for value in &self.blob_tables {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.fts_l0_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.cf != 0 {
            os.write_int32(1, self.cf)?;
        }
        if self.level != 0 {
            os.write_uint32(2, self.level)?;
        }
        for v in &self.table_creates {
            os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.top_deletes {
            os.write_uint64(4, *v)?;
        };
        for v in &self.bottom_deletes {
            os.write_uint64(5, *v)?;
        };
        if self.conflicted != false {
            os.write_bool(6, self.conflicted)?;
        }
        for v in &self.blob_tables {
            os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.fts_l0_creates {
            os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Compaction {
        Compaction::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                    "cf",
                    |m: &Compaction| { &m.cf },
                    |m: &mut Compaction| { &mut m.cf },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "level",
                    |m: &Compaction| { &m.level },
                    |m: &mut Compaction| { &mut m.level },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableCreate>>(
                    "tableCreates",
                    |m: &Compaction| { &m.table_creates },
                    |m: &mut Compaction| { &mut m.table_creates },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "topDeletes",
                    |m: &Compaction| { &m.top_deletes },
                    |m: &mut Compaction| { &mut m.top_deletes },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "bottomDeletes",
                    |m: &Compaction| { &m.bottom_deletes },
                    |m: &mut Compaction| { &mut m.bottom_deletes },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                    "conflicted",
                    |m: &Compaction| { &m.conflicted },
                    |m: &mut Compaction| { &mut m.conflicted },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<BlobCreate>>(
                    "blobTables",
                    |m: &Compaction| { &m.blob_tables },
                    |m: &mut Compaction| { &mut m.blob_tables },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FtsL0Create>>(
                    "fts_l0_creates",
                    |m: &Compaction| { &m.fts_l0_creates },
                    |m: &mut Compaction| { &mut m.fts_l0_creates },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<Compaction>(
                    "Compaction",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static Compaction {
        static mut instance: ::protobuf::lazy::Lazy<Compaction> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const Compaction,
        };
        unsafe {
            instance.get(Compaction::new)
        }
    }
}

impl ::protobuf::Clear for Compaction {
    fn clear(&mut self) {
        self.cf = 0;
        self.level = 0;
        self.table_creates.clear();
        self.top_deletes.clear();
        self.bottom_deletes.clear();
        self.conflicted = false;
        self.blob_tables.clear();
        self.fts_l0_creates.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for Compaction {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.cf, "cf", buf);
        ::protobuf::PbPrint::fmt(&self.level, "level", buf);
        ::protobuf::PbPrint::fmt(&self.table_creates, "table_creates", buf);
        ::protobuf::PbPrint::fmt(&self.top_deletes, "top_deletes", buf);
        ::protobuf::PbPrint::fmt(&self.bottom_deletes, "bottom_deletes", buf);
        ::protobuf::PbPrint::fmt(&self.conflicted, "conflicted", buf);
        ::protobuf::PbPrint::fmt(&self.blob_tables, "blob_tables", buf);
        ::protobuf::PbPrint::fmt(&self.fts_l0_creates, "fts_l0_creates", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for Compaction {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.cf, "cf", &mut s);
        ::protobuf::PbPrint::fmt(&self.level, "level", &mut s);
        ::protobuf::PbPrint::fmt(&self.table_creates, "table_creates", &mut s);
        ::protobuf::PbPrint::fmt(&self.top_deletes, "top_deletes", &mut s);
        ::protobuf::PbPrint::fmt(&self.bottom_deletes, "bottom_deletes", &mut s);
        ::protobuf::PbPrint::fmt(&self.conflicted, "conflicted", &mut s);
        ::protobuf::PbPrint::fmt(&self.blob_tables, "blob_tables", &mut s);
        ::protobuf::PbPrint::fmt(&self.fts_l0_creates, "fts_l0_creates", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for Compaction {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct MajorCompaction {
    // message fields
    pub sstable_change: ::protobuf::SingularPtrField<TableChange>,
    pub new_blob_tables: ::protobuf::RepeatedField<BlobCreate>,
    pub old_blob_tables: ::std::vec::Vec<u64>,
    pub conflicted: bool,
    pub update_inner_key_offset: bool,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a MajorCompaction {
    fn default() -> &'a MajorCompaction {
        <MajorCompaction as ::protobuf::Message>::default_instance()
    }
}

impl MajorCompaction {
    pub fn new() -> MajorCompaction {
        ::std::default::Default::default()
    }

    // .enginepb.TableChange sstableChange = 1;


    pub fn get_sstable_change(&self) -> &TableChange {
        self.sstable_change.as_ref().unwrap_or_else(|| TableChange::default_instance())
    }
    pub fn clear_sstable_change(&mut self) {
        self.sstable_change.clear();
    }

    pub fn has_sstable_change(&self) -> bool {
        self.sstable_change.is_some()
    }

    // Param is passed by value, moved
    pub fn set_sstable_change(&mut self, v: TableChange) {
        self.sstable_change = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_sstable_change(&mut self) -> &mut TableChange {
        if self.sstable_change.is_none() {
            self.sstable_change.set_default();
        }
        self.sstable_change.as_mut().unwrap()
    }

    // Take field
    pub fn take_sstable_change(&mut self) -> TableChange {
        self.sstable_change.take().unwrap_or_else(|| TableChange::new())
    }

    // repeated .enginepb.BlobCreate newBlobTables = 2;


    pub fn get_new_blob_tables(&self) -> &[BlobCreate] {
        &self.new_blob_tables
    }
    pub fn clear_new_blob_tables(&mut self) {
        self.new_blob_tables.clear();
    }

    // Param is passed by value, moved
    pub fn set_new_blob_tables(&mut self, v: ::protobuf::RepeatedField<BlobCreate>) {
        self.new_blob_tables = v;
    }

    // Mutable pointer to the field.
    pub fn mut_new_blob_tables(&mut self) -> &mut ::protobuf::RepeatedField<BlobCreate> {
        &mut self.new_blob_tables
    }

    // Take field
    pub fn take_new_blob_tables(&mut self) -> ::protobuf::RepeatedField<BlobCreate> {
        ::std::mem::replace(&mut self.new_blob_tables, ::protobuf::RepeatedField::new())
    }

    // repeated uint64 oldBlobTables = 3;


    pub fn get_old_blob_tables(&self) -> &[u64] {
        &self.old_blob_tables
    }
    pub fn clear_old_blob_tables(&mut self) {
        self.old_blob_tables.clear();
    }

    // Param is passed by value, moved
    pub fn set_old_blob_tables(&mut self, v: ::std::vec::Vec<u64>) {
        self.old_blob_tables = v;
    }

    // Mutable pointer to the field.
    pub fn mut_old_blob_tables(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.old_blob_tables
    }

    // Take field
    pub fn take_old_blob_tables(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.old_blob_tables, ::std::vec::Vec::new())
    }

    // bool conflicted = 4;


    pub fn get_conflicted(&self) -> bool {
        self.conflicted
    }
    pub fn clear_conflicted(&mut self) {
        self.conflicted = false;
    }

    // Param is passed by value, moved
    pub fn set_conflicted(&mut self, v: bool) {
        self.conflicted = v;
    }

    // bool update_inner_key_offset = 5;


    pub fn get_update_inner_key_offset(&self) -> bool {
        self.update_inner_key_offset
    }
    pub fn clear_update_inner_key_offset(&mut self) {
        self.update_inner_key_offset = false;
    }

    // Param is passed by value, moved
    pub fn set_update_inner_key_offset(&mut self, v: bool) {
        self.update_inner_key_offset = v;
    }
}

impl ::protobuf::Message for MajorCompaction {
    fn is_initialized(&self) -> bool {
        for v in &self.sstable_change {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.new_blob_tables {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.sstable_change)?;
                },
                2 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.new_blob_tables)?;
                },
                3 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.old_blob_tables)?;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.conflicted = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.update_inner_key_offset = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.sstable_change.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        for value in &self.new_blob_tables {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.old_blob_tables {
            my_size += ::protobuf::rt::value_size(3, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        if self.conflicted != false {
            my_size += 2;
        }
        if self.update_inner_key_offset != false {
            my_size += 2;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.sstable_change.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        for v in &self.new_blob_tables {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.old_blob_tables {
            os.write_uint64(3, *v)?;
        };
        if self.conflicted != false {
            os.write_bool(4, self.conflicted)?;
        }
        if self.update_inner_key_offset != false {
            os.write_bool(5, self.update_inner_key_offset)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> MajorCompaction {
        MajorCompaction::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableChange>>(
                    "sstableChange",
                    |m: &MajorCompaction| { &m.sstable_change },
                    |m: &mut MajorCompaction| { &mut m.sstable_change },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<BlobCreate>>(
                    "newBlobTables",
                    |m: &MajorCompaction| { &m.new_blob_tables },
                    |m: &mut MajorCompaction| { &mut m.new_blob_tables },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "oldBlobTables",
                    |m: &MajorCompaction| { &m.old_blob_tables },
                    |m: &mut MajorCompaction| { &mut m.old_blob_tables },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                    "conflicted",
                    |m: &MajorCompaction| { &m.conflicted },
                    |m: &mut MajorCompaction| { &mut m.conflicted },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                    "update_inner_key_offset",
                    |m: &MajorCompaction| { &m.update_inner_key_offset },
                    |m: &mut MajorCompaction| { &mut m.update_inner_key_offset },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<MajorCompaction>(
                    "MajorCompaction",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static MajorCompaction {
        static mut instance: ::protobuf::lazy::Lazy<MajorCompaction> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const MajorCompaction,
        };
        unsafe {
            instance.get(MajorCompaction::new)
        }
    }
}

impl ::protobuf::Clear for MajorCompaction {
    fn clear(&mut self) {
        self.sstable_change.clear();
        self.new_blob_tables.clear();
        self.old_blob_tables.clear();
        self.conflicted = false;
        self.update_inner_key_offset = false;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for MajorCompaction {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.sstable_change, "sstable_change", buf);
        ::protobuf::PbPrint::fmt(&self.new_blob_tables, "new_blob_tables", buf);
        ::protobuf::PbPrint::fmt(&self.old_blob_tables, "old_blob_tables", buf);
        ::protobuf::PbPrint::fmt(&self.conflicted, "conflicted", buf);
        ::protobuf::PbPrint::fmt(&self.update_inner_key_offset, "update_inner_key_offset", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for MajorCompaction {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.sstable_change, "sstable_change", &mut s);
        ::protobuf::PbPrint::fmt(&self.new_blob_tables, "new_blob_tables", &mut s);
        ::protobuf::PbPrint::fmt(&self.old_blob_tables, "old_blob_tables", &mut s);
        ::protobuf::PbPrint::fmt(&self.conflicted, "conflicted", &mut s);
        ::protobuf::PbPrint::fmt(&self.update_inner_key_offset, "update_inner_key_offset", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for MajorCompaction {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct SchemaMeta {
    // message fields
    pub keyspace_id: u32,
    pub file_id: u64,
    pub version: i64,
    pub restore_version: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a SchemaMeta {
    fn default() -> &'a SchemaMeta {
        <SchemaMeta as ::protobuf::Message>::default_instance()
    }
}

impl SchemaMeta {
    pub fn new() -> SchemaMeta {
        ::std::default::Default::default()
    }

    // uint32 keyspace_id = 1;


    pub fn get_keyspace_id(&self) -> u32 {
        self.keyspace_id
    }
    pub fn clear_keyspace_id(&mut self) {
        self.keyspace_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_keyspace_id(&mut self, v: u32) {
        self.keyspace_id = v;
    }

    // uint64 file_id = 2;


    pub fn get_file_id(&self) -> u64 {
        self.file_id
    }
    pub fn clear_file_id(&mut self) {
        self.file_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_file_id(&mut self, v: u64) {
        self.file_id = v;
    }

    // int64 version = 3;


    pub fn get_version(&self) -> i64 {
        self.version
    }
    pub fn clear_version(&mut self) {
        self.version = 0;
    }

    // Param is passed by value, moved
    pub fn set_version(&mut self, v: i64) {
        self.version = v;
    }

    // uint64 restore_version = 4;


    pub fn get_restore_version(&self) -> u64 {
        self.restore_version
    }
    pub fn clear_restore_version(&mut self) {
        self.restore_version = 0;
    }

    // Param is passed by value, moved
    pub fn set_restore_version(&mut self, v: u64) {
        self.restore_version = v;
    }
}

impl ::protobuf::Message for SchemaMeta {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.keyspace_id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.file_id = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.version = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.restore_version = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.keyspace_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.keyspace_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.file_id != 0 {
            my_size += ::protobuf::rt::value_size(2, self.file_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.version != 0 {
            my_size += ::protobuf::rt::value_size(3, self.version, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.restore_version != 0 {
            my_size += ::protobuf::rt::value_size(4, self.restore_version, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.keyspace_id != 0 {
            os.write_uint32(1, self.keyspace_id)?;
        }
        if self.file_id != 0 {
            os.write_uint64(2, self.file_id)?;
        }
        if self.version != 0 {
            os.write_int64(3, self.version)?;
        }
        if self.restore_version != 0 {
            os.write_uint64(4, self.restore_version)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> SchemaMeta {
        SchemaMeta::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "keyspace_id",
                    |m: &SchemaMeta| { &m.keyspace_id },
                    |m: &mut SchemaMeta| { &mut m.keyspace_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "file_id",
                    |m: &SchemaMeta| { &m.file_id },
                    |m: &mut SchemaMeta| { &mut m.file_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "version",
                    |m: &SchemaMeta| { &m.version },
                    |m: &mut SchemaMeta| { &mut m.version },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "restore_version",
                    |m: &SchemaMeta| { &m.restore_version },
                    |m: &mut SchemaMeta| { &mut m.restore_version },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<SchemaMeta>(
                    "SchemaMeta",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static SchemaMeta {
        static mut instance: ::protobuf::lazy::Lazy<SchemaMeta> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const SchemaMeta,
        };
        unsafe {
            instance.get(SchemaMeta::new)
        }
    }
}

impl ::protobuf::Clear for SchemaMeta {
    fn clear(&mut self) {
        self.keyspace_id = 0;
        self.file_id = 0;
        self.version = 0;
        self.restore_version = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for SchemaMeta {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.keyspace_id, "keyspace_id", buf);
        ::protobuf::PbPrint::fmt(&self.file_id, "file_id", buf);
        ::protobuf::PbPrint::fmt(&self.version, "version", buf);
        ::protobuf::PbPrint::fmt(&self.restore_version, "restore_version", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for SchemaMeta {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.keyspace_id, "keyspace_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.file_id, "file_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.version, "version", &mut s);
        ::protobuf::PbPrint::fmt(&self.restore_version, "restore_version", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for SchemaMeta {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ColumnarCompaction {
    // message fields
    pub columnar_change: ::protobuf::SingularPtrField<TableChange>,
    pub snap_version: u64,
    pub row_l0s: ::std::vec::Vec<u64>,
    pub target_level: u32,
    pub columnar_table_ids: ::std::vec::Vec<i64>,
    pub columnar_table_ids_to_clear: ::std::vec::Vec<i64>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ColumnarCompaction {
    fn default() -> &'a ColumnarCompaction {
        <ColumnarCompaction as ::protobuf::Message>::default_instance()
    }
}

impl ColumnarCompaction {
    pub fn new() -> ColumnarCompaction {
        ::std::default::Default::default()
    }

    // .enginepb.TableChange columnar_change = 1;


    pub fn get_columnar_change(&self) -> &TableChange {
        self.columnar_change.as_ref().unwrap_or_else(|| TableChange::default_instance())
    }
    pub fn clear_columnar_change(&mut self) {
        self.columnar_change.clear();
    }

    pub fn has_columnar_change(&self) -> bool {
        self.columnar_change.is_some()
    }

    // Param is passed by value, moved
    pub fn set_columnar_change(&mut self, v: TableChange) {
        self.columnar_change = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_columnar_change(&mut self) -> &mut TableChange {
        if self.columnar_change.is_none() {
            self.columnar_change.set_default();
        }
        self.columnar_change.as_mut().unwrap()
    }

    // Take field
    pub fn take_columnar_change(&mut self) -> TableChange {
        self.columnar_change.take().unwrap_or_else(|| TableChange::new())
    }

    // uint64 snap_version = 2;


    pub fn get_snap_version(&self) -> u64 {
        self.snap_version
    }
    pub fn clear_snap_version(&mut self) {
        self.snap_version = 0;
    }

    // Param is passed by value, moved
    pub fn set_snap_version(&mut self, v: u64) {
        self.snap_version = v;
    }

    // repeated uint64 row_l0s = 3;


    pub fn get_row_l0s(&self) -> &[u64] {
        &self.row_l0s
    }
    pub fn clear_row_l0s(&mut self) {
        self.row_l0s.clear();
    }

    // Param is passed by value, moved
    pub fn set_row_l0s(&mut self, v: ::std::vec::Vec<u64>) {
        self.row_l0s = v;
    }

    // Mutable pointer to the field.
    pub fn mut_row_l0s(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.row_l0s
    }

    // Take field
    pub fn take_row_l0s(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.row_l0s, ::std::vec::Vec::new())
    }

    // uint32 target_level = 4;


    pub fn get_target_level(&self) -> u32 {
        self.target_level
    }
    pub fn clear_target_level(&mut self) {
        self.target_level = 0;
    }

    // Param is passed by value, moved
    pub fn set_target_level(&mut self, v: u32) {
        self.target_level = v;
    }

    // repeated int64 columnar_table_ids = 5;


    pub fn get_columnar_table_ids(&self) -> &[i64] {
        &self.columnar_table_ids
    }
    pub fn clear_columnar_table_ids(&mut self) {
        self.columnar_table_ids.clear();
    }

    // Param is passed by value, moved
    pub fn set_columnar_table_ids(&mut self, v: ::std::vec::Vec<i64>) {
        self.columnar_table_ids = v;
    }

    // Mutable pointer to the field.
    pub fn mut_columnar_table_ids(&mut self) -> &mut ::std::vec::Vec<i64> {
        &mut self.columnar_table_ids
    }

    // Take field
    pub fn take_columnar_table_ids(&mut self) -> ::std::vec::Vec<i64> {
        ::std::mem::replace(&mut self.columnar_table_ids, ::std::vec::Vec::new())
    }

    // repeated int64 columnar_table_ids_to_clear = 6;


    pub fn get_columnar_table_ids_to_clear(&self) -> &[i64] {
        &self.columnar_table_ids_to_clear
    }
    pub fn clear_columnar_table_ids_to_clear(&mut self) {
        self.columnar_table_ids_to_clear.clear();
    }

    // Param is passed by value, moved
    pub fn set_columnar_table_ids_to_clear(&mut self, v: ::std::vec::Vec<i64>) {
        self.columnar_table_ids_to_clear = v;
    }

    // Mutable pointer to the field.
    pub fn mut_columnar_table_ids_to_clear(&mut self) -> &mut ::std::vec::Vec<i64> {
        &mut self.columnar_table_ids_to_clear
    }

    // Take field
    pub fn take_columnar_table_ids_to_clear(&mut self) -> ::std::vec::Vec<i64> {
        ::std::mem::replace(&mut self.columnar_table_ids_to_clear, ::std::vec::Vec::new())
    }
}

impl ::protobuf::Message for ColumnarCompaction {
    fn is_initialized(&self) -> bool {
        for v in &self.columnar_change {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.columnar_change)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.snap_version = tmp;
                },
                3 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.row_l0s)?;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.target_level = tmp;
                },
                5 => {
                    ::protobuf::rt::read_repeated_int64_into(wire_type, is, &mut self.columnar_table_ids)?;
                },
                6 => {
                    ::protobuf::rt::read_repeated_int64_into(wire_type, is, &mut self.columnar_table_ids_to_clear)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.columnar_change.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.snap_version != 0 {
            my_size += ::protobuf::rt::value_size(2, self.snap_version, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.row_l0s {
            my_size += ::protobuf::rt::value_size(3, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        if self.target_level != 0 {
            my_size += ::protobuf::rt::value_size(4, self.target_level, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.columnar_table_ids {
            my_size += ::protobuf::rt::value_size(5, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        for value in &self.columnar_table_ids_to_clear {
            my_size += ::protobuf::rt::value_size(6, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.columnar_change.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.snap_version != 0 {
            os.write_uint64(2, self.snap_version)?;
        }
        for v in &self.row_l0s {
            os.write_uint64(3, *v)?;
        };
        if self.target_level != 0 {
            os.write_uint32(4, self.target_level)?;
        }
        for v in &self.columnar_table_ids {
            os.write_int64(5, *v)?;
        };
        for v in &self.columnar_table_ids_to_clear {
            os.write_int64(6, *v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ColumnarCompaction {
        ColumnarCompaction::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableChange>>(
                    "columnar_change",
                    |m: &ColumnarCompaction| { &m.columnar_change },
                    |m: &mut ColumnarCompaction| { &mut m.columnar_change },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "snap_version",
                    |m: &ColumnarCompaction| { &m.snap_version },
                    |m: &mut ColumnarCompaction| { &mut m.snap_version },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "row_l0s",
                    |m: &ColumnarCompaction| { &m.row_l0s },
                    |m: &mut ColumnarCompaction| { &mut m.row_l0s },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "target_level",
                    |m: &ColumnarCompaction| { &m.target_level },
                    |m: &mut ColumnarCompaction| { &mut m.target_level },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "columnar_table_ids",
                    |m: &ColumnarCompaction| { &m.columnar_table_ids },
                    |m: &mut ColumnarCompaction| { &mut m.columnar_table_ids },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "columnar_table_ids_to_clear",
                    |m: &ColumnarCompaction| { &m.columnar_table_ids_to_clear },
                    |m: &mut ColumnarCompaction| { &mut m.columnar_table_ids_to_clear },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<ColumnarCompaction>(
                    "ColumnarCompaction",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static ColumnarCompaction {
        static mut instance: ::protobuf::lazy::Lazy<ColumnarCompaction> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ColumnarCompaction,
        };
        unsafe {
            instance.get(ColumnarCompaction::new)
        }
    }
}

impl ::protobuf::Clear for ColumnarCompaction {
    fn clear(&mut self) {
        self.columnar_change.clear();
        self.snap_version = 0;
        self.row_l0s.clear();
        self.target_level = 0;
        self.columnar_table_ids.clear();
        self.columnar_table_ids_to_clear.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for ColumnarCompaction {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.columnar_change, "columnar_change", buf);
        ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", buf);
        ::protobuf::PbPrint::fmt(&self.row_l0s, "row_l0s", buf);
        ::protobuf::PbPrint::fmt(&self.target_level, "target_level", buf);
        ::protobuf::PbPrint::fmt(&self.columnar_table_ids, "columnar_table_ids", buf);
        ::protobuf::PbPrint::fmt(&self.columnar_table_ids_to_clear, "columnar_table_ids_to_clear", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for ColumnarCompaction {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.columnar_change, "columnar_change", &mut s);
        ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", &mut s);
        ::protobuf::PbPrint::fmt(&self.row_l0s, "row_l0s", &mut s);
        ::protobuf::PbPrint::fmt(&self.target_level, "target_level", &mut s);
        ::protobuf::PbPrint::fmt(&self.columnar_table_ids, "columnar_table_ids", &mut s);
        ::protobuf::PbPrint::fmt(&self.columnar_table_ids_to_clear, "columnar_table_ids_to_clear", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for ColumnarCompaction {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct UpdateVectorIndex {
    // message fields
    pub table_id: i64,
    pub index_id: i64,
    pub col_id: i64,
    pub added: ::protobuf::RepeatedField<VectorIndexFile>,
    pub removed: ::std::vec::Vec<u64>,
    pub snap_version: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UpdateVectorIndex {
    fn default() -> &'a UpdateVectorIndex {
        <UpdateVectorIndex as ::protobuf::Message>::default_instance()
    }
}

impl UpdateVectorIndex {
    pub fn new() -> UpdateVectorIndex {
        ::std::default::Default::default()
    }

    // int64 table_id = 1;


    pub fn get_table_id(&self) -> i64 {
        self.table_id
    }
    pub fn clear_table_id(&mut self) {
        self.table_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_table_id(&mut self, v: i64) {
        self.table_id = v;
    }

    // int64 index_id = 2;


    pub fn get_index_id(&self) -> i64 {
        self.index_id
    }
    pub fn clear_index_id(&mut self) {
        self.index_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_index_id(&mut self, v: i64) {
        self.index_id = v;
    }

    // int64 col_id = 3;


    pub fn get_col_id(&self) -> i64 {
        self.col_id
    }
    pub fn clear_col_id(&mut self) {
        self.col_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_col_id(&mut self, v: i64) {
        self.col_id = v;
    }

    // repeated .enginepb.VectorIndexFile added = 4;


    pub fn get_added(&self) -> &[VectorIndexFile] {
        &self.added
    }
    pub fn clear_added(&mut self) {
        self.added.clear();
    }

    // Param is passed by value, moved
    pub fn set_added(&mut self, v: ::protobuf::RepeatedField<VectorIndexFile>) {
        self.added = v;
    }

    // Mutable pointer to the field.
    pub fn mut_added(&mut self) -> &mut ::protobuf::RepeatedField<VectorIndexFile> {
        &mut self.added
    }

    // Take field
    pub fn take_added(&mut self) -> ::protobuf::RepeatedField<VectorIndexFile> {
        ::std::mem::replace(&mut self.added, ::protobuf::RepeatedField::new())
    }

    // repeated uint64 removed = 5;


    pub fn get_removed(&self) -> &[u64] {
        &self.removed
    }
    pub fn clear_removed(&mut self) {
        self.removed.clear();
    }

    // Param is passed by value, moved
    pub fn set_removed(&mut self, v: ::std::vec::Vec<u64>) {
        self.removed = v;
    }

    // Mutable pointer to the field.
    pub fn mut_removed(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.removed
    }

    // Take field
    pub fn take_removed(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.removed, ::std::vec::Vec::new())
    }

    // uint64 snap_version = 6;


    pub fn get_snap_version(&self) -> u64 {
        self.snap_version
    }
    pub fn clear_snap_version(&mut self) {
        self.snap_version = 0;
    }

    // Param is passed by value, moved
    pub fn set_snap_version(&mut self, v: u64) {
        self.snap_version = v;
    }
}

impl ::protobuf::Message for UpdateVectorIndex {
    fn is_initialized(&self) -> bool {
        for v in &self.added {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.table_id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.index_id = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.col_id = tmp;
                },
                4 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.added)?;
                },
                5 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.removed)?;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.snap_version = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.table_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.table_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.index_id != 0 {
            my_size += ::protobuf::rt::value_size(2, self.index_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.col_id != 0 {
            my_size += ::protobuf::rt::value_size(3, self.col_id, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.added {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.removed {
            my_size += ::protobuf::rt::value_size(5, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        if self.snap_version != 0 {
            my_size += ::protobuf::rt::value_size(6, self.snap_version, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.table_id != 0 {
            os.write_int64(1, self.table_id)?;
        }
        if self.index_id != 0 {
            os.write_int64(2, self.index_id)?;
        }
        if self.col_id != 0 {
            os.write_int64(3, self.col_id)?;
        }
        for v in &self.added {
            os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.removed {
            os.write_uint64(5, *v)?;
        };
        if self.snap_version != 0 {
            os.write_uint64(6, self.snap_version)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UpdateVectorIndex {
        UpdateVectorIndex::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "table_id",
                    |m: &UpdateVectorIndex| { &m.table_id },
                    |m: &mut UpdateVectorIndex| { &mut m.table_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "index_id",
                    |m: &UpdateVectorIndex| { &m.index_id },
                    |m: &mut UpdateVectorIndex| { &mut m.index_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "col_id",
                    |m: &UpdateVectorIndex| { &m.col_id },
                    |m: &mut UpdateVectorIndex| { &mut m.col_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<VectorIndexFile>>(
                    "added",
                    |m: &UpdateVectorIndex| { &m.added },
                    |m: &mut UpdateVectorIndex| { &mut m.added },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "removed",
                    |m: &UpdateVectorIndex| { &m.removed },
                    |m: &mut UpdateVectorIndex| { &mut m.removed },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "snap_version",
                    |m: &UpdateVectorIndex| { &m.snap_version },
                    |m: &mut UpdateVectorIndex| { &mut m.snap_version },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<UpdateVectorIndex>(
                    "UpdateVectorIndex",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static UpdateVectorIndex {
        static mut instance: ::protobuf::lazy::Lazy<UpdateVectorIndex> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const UpdateVectorIndex,
        };
        unsafe {
            instance.get(UpdateVectorIndex::new)
        }
    }
}

impl ::protobuf::Clear for UpdateVectorIndex {
    fn clear(&mut self) {
        self.table_id = 0;
        self.index_id = 0;
        self.col_id = 0;
        self.added.clear();
        self.removed.clear();
        self.snap_version = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for UpdateVectorIndex {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.table_id, "table_id", buf);
        ::protobuf::PbPrint::fmt(&self.index_id, "index_id", buf);
        ::protobuf::PbPrint::fmt(&self.col_id, "col_id", buf);
        ::protobuf::PbPrint::fmt(&self.added, "added", buf);
        ::protobuf::PbPrint::fmt(&self.removed, "removed", buf);
        ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for UpdateVectorIndex {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.table_id, "table_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.index_id, "index_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.col_id, "col_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.added, "added", &mut s);
        ::protobuf::PbPrint::fmt(&self.removed, "removed", &mut s);
        ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for UpdateVectorIndex {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct Flush {
    // message fields
    pub l0_create: ::protobuf::SingularPtrField<L0Create>,
    pub properties: ::protobuf::SingularPtrField<Properties>,
    pub version: u64,
    pub max_ts: u64,
    pub l0_creates: ::protobuf::RepeatedField<L0Create>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Flush {
    fn default() -> &'a Flush {
        <Flush as ::protobuf::Message>::default_instance()
    }
}

impl Flush {
    pub fn new() -> Flush {
        ::std::default::Default::default()
    }

    // .enginepb.L0Create l0Create = 1;


    pub fn get_l0_create(&self) -> &L0Create {
        self.l0_create.as_ref().unwrap_or_else(|| L0Create::default_instance())
    }
    pub fn clear_l0_create(&mut self) {
        self.l0_create.clear();
    }

    pub fn has_l0_create(&self) -> bool {
        self.l0_create.is_some()
    }

    // Param is passed by value, moved
    pub fn set_l0_create(&mut self, v: L0Create) {
        self.l0_create = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_l0_create(&mut self) -> &mut L0Create {
        if self.l0_create.is_none() {
            self.l0_create.set_default();
        }
        self.l0_create.as_mut().unwrap()
    }

    // Take field
    pub fn take_l0_create(&mut self) -> L0Create {
        self.l0_create.take().unwrap_or_else(|| L0Create::new())
    }

    // .enginepb.Properties properties = 2;


    pub fn get_properties(&self) -> &Properties {
        self.properties.as_ref().unwrap_or_else(|| Properties::default_instance())
    }
    pub fn clear_properties(&mut self) {
        self.properties.clear();
    }

    pub fn has_properties(&self) -> bool {
        self.properties.is_some()
    }

    // Param is passed by value, moved
    pub fn set_properties(&mut self, v: Properties) {
        self.properties = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_properties(&mut self) -> &mut Properties {
        if self.properties.is_none() {
            self.properties.set_default();
        }
        self.properties.as_mut().unwrap()
    }

    // Take field
    pub fn take_properties(&mut self) -> Properties {
        self.properties.take().unwrap_or_else(|| Properties::new())
    }

    // uint64 version = 3;


    pub fn get_version(&self) -> u64 {
        self.version
    }
    pub fn clear_version(&mut self) {
        self.version = 0;
    }

    // Param is passed by value, moved
    pub fn set_version(&mut self, v: u64) {
        self.version = v;
    }

    // uint64 max_ts = 5;


    pub fn get_max_ts(&self) -> u64 {
        self.max_ts
    }
    pub fn clear_max_ts(&mut self) {
        self.max_ts = 0;
    }

    // Param is passed by value, moved
    pub fn set_max_ts(&mut self, v: u64) {
        self.max_ts = v;
    }

    // repeated .enginepb.L0Create l0Creates = 6;


    pub fn get_l0_creates(&self) -> &[L0Create] {
        &self.l0_creates
    }
    pub fn clear_l0_creates(&mut self) {
        self.l0_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_l0_creates(&mut self, v: ::protobuf::RepeatedField<L0Create>) {
        self.l0_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_l0_creates(&mut self) -> &mut ::protobuf::RepeatedField<L0Create> {
        &mut self.l0_creates
    }

    // Take field
    pub fn take_l0_creates(&mut self) -> ::protobuf::RepeatedField<L0Create> {
        ::std::mem::replace(&mut self.l0_creates, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for Flush {
    fn is_initialized(&self) -> bool {
        for v in &self.l0_create {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.properties {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.l0_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.l0_create)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.properties)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.version = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.max_ts = tmp;
                },
                6 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.l0_creates)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.l0_create.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let Some(ref v) = self.properties.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.version != 0 {
            my_size += ::protobuf::rt::value_size(3, self.version, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.max_ts != 0 {
            my_size += ::protobuf::rt::value_size(5, self.max_ts, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.l0_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.l0_create.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let Some(ref v) = self.properties.as_ref() {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.version != 0 {
            os.write_uint64(3, self.version)?;
        }
        if self.max_ts != 0 {
            os.write_uint64(5, self.max_ts)?;
        }
        for v in &self.l0_creates {
            os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Flush {
        Flush::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<L0Create>>(
                    "l0Create",
                    |m: &Flush| { &m.l0_create },
                    |m: &mut Flush| { &mut m.l0_create },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Properties>>(
                    "properties",
                    |m: &Flush| { &m.properties },
                    |m: &mut Flush| { &mut m.properties },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "version",
                    |m: &Flush| { &m.version },
                    |m: &mut Flush| { &mut m.version },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "max_ts",
                    |m: &Flush| { &m.max_ts },
                    |m: &mut Flush| { &mut m.max_ts },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<L0Create>>(
                    "l0Creates",
                    |m: &Flush| { &m.l0_creates },
                    |m: &mut Flush| { &mut m.l0_creates },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<Flush>(
                    "Flush",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static Flush {
        static mut instance: ::protobuf::lazy::Lazy<Flush> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const Flush,
        };
        unsafe {
            instance.get(Flush::new)
        }
    }
}

impl ::protobuf::Clear for Flush {
    fn clear(&mut self) {
        self.l0_create.clear();
        self.properties.clear();
        self.version = 0;
        self.max_ts = 0;
        self.l0_creates.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for Flush {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.l0_create, "l0_create", buf);
        ::protobuf::PbPrint::fmt(&self.properties, "properties", buf);
        ::protobuf::PbPrint::fmt(&self.version, "version", buf);
        ::protobuf::PbPrint::fmt(&self.max_ts, "max_ts", buf);
        ::protobuf::PbPrint::fmt(&self.l0_creates, "l0_creates", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for Flush {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.l0_create, "l0_create", &mut s);
        ::protobuf::PbPrint::fmt(&self.properties, "properties", &mut s);
        ::protobuf::PbPrint::fmt(&self.version, "version", &mut s);
        ::protobuf::PbPrint::fmt(&self.max_ts, "max_ts", &mut s);
        ::protobuf::PbPrint::fmt(&self.l0_creates, "l0_creates", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for Flush {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct Snapshot {
    // message fields
    pub outer_start: ::std::vec::Vec<u8>,
    pub outer_end: ::std::vec::Vec<u8>,
    pub properties: ::protobuf::SingularPtrField<Properties>,
    pub l0_creates: ::protobuf::RepeatedField<L0Create>,
    pub table_creates: ::protobuf::RepeatedField<TableCreate>,
    pub base_version: u64,
    pub data_sequence: u64,
    pub blob_creates: ::protobuf::RepeatedField<BlobCreate>,
    pub max_ts: u64,
    pub inner_key_off: u32,
    pub columnar_creates: ::protobuf::RepeatedField<ColumnarCreate>,
    pub schema_meta: ::protobuf::SingularPtrField<SchemaMeta>,
    pub unconverted_l0s: ::std::vec::Vec<u64>,
    pub vector_indexes: ::protobuf::RepeatedField<VectorIndex>,
    pub columnar_table_ids: ::std::vec::Vec<i64>,
    pub columnar_l2_snap_version: u64,
    pub fts_l0_segments: ::protobuf::RepeatedField<FtsL0Create>,
    pub fts_unconverted_l0s: ::std::vec::Vec<u64>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Snapshot {
    fn default() -> &'a Snapshot {
        <Snapshot as ::protobuf::Message>::default_instance()
    }
}

impl Snapshot {
    pub fn new() -> Snapshot {
        ::std::default::Default::default()
    }

    // bytes outer_start = 1;


    pub fn get_outer_start(&self) -> &[u8] {
        &self.outer_start
    }
    pub fn clear_outer_start(&mut self) {
        self.outer_start.clear();
    }

    // Param is passed by value, moved
    pub fn set_outer_start(&mut self, v: ::std::vec::Vec<u8>) {
        self.outer_start = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_outer_start(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.outer_start
    }

    // Take field
    pub fn take_outer_start(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.outer_start, ::std::vec::Vec::new())
    }

    // bytes outer_end = 2;


    pub fn get_outer_end(&self) -> &[u8] {
        &self.outer_end
    }
    pub fn clear_outer_end(&mut self) {
        self.outer_end.clear();
    }

    // Param is passed by value, moved
    pub fn set_outer_end(&mut self, v: ::std::vec::Vec<u8>) {
        self.outer_end = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_outer_end(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.outer_end
    }

    // Take field
    pub fn take_outer_end(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.outer_end, ::std::vec::Vec::new())
    }

    // .enginepb.Properties properties = 3;


    pub fn get_properties(&self) -> &Properties {
        self.properties.as_ref().unwrap_or_else(|| Properties::default_instance())
    }
    pub fn clear_properties(&mut self) {
        self.properties.clear();
    }

    pub fn has_properties(&self) -> bool {
        self.properties.is_some()
    }

    // Param is passed by value, moved
    pub fn set_properties(&mut self, v: Properties) {
        self.properties = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_properties(&mut self) -> &mut Properties {
        if self.properties.is_none() {
            self.properties.set_default();
        }
        self.properties.as_mut().unwrap()
    }

    // Take field
    pub fn take_properties(&mut self) -> Properties {
        self.properties.take().unwrap_or_else(|| Properties::new())
    }

    // repeated .enginepb.L0Create l0Creates = 5;


    pub fn get_l0_creates(&self) -> &[L0Create] {
        &self.l0_creates
    }
    pub fn clear_l0_creates(&mut self) {
        self.l0_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_l0_creates(&mut self, v: ::protobuf::RepeatedField<L0Create>) {
        self.l0_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_l0_creates(&mut self) -> &mut ::protobuf::RepeatedField<L0Create> {
        &mut self.l0_creates
    }

    // Take field
    pub fn take_l0_creates(&mut self) -> ::protobuf::RepeatedField<L0Create> {
        ::std::mem::replace(&mut self.l0_creates, ::protobuf::RepeatedField::new())
    }

    // repeated .enginepb.TableCreate tableCreates = 6;


    pub fn get_table_creates(&self) -> &[TableCreate] {
        &self.table_creates
    }
    pub fn clear_table_creates(&mut self) {
        self.table_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_table_creates(&mut self, v: ::protobuf::RepeatedField<TableCreate>) {
        self.table_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_table_creates(&mut self) -> &mut ::protobuf::RepeatedField<TableCreate> {
        &mut self.table_creates
    }

    // Take field
    pub fn take_table_creates(&mut self) -> ::protobuf::RepeatedField<TableCreate> {
        ::std::mem::replace(&mut self.table_creates, ::protobuf::RepeatedField::new())
    }

    // uint64 baseVersion = 7;


    pub fn get_base_version(&self) -> u64 {
        self.base_version
    }
    pub fn clear_base_version(&mut self) {
        self.base_version = 0;
    }

    // Param is passed by value, moved
    pub fn set_base_version(&mut self, v: u64) {
        self.base_version = v;
    }

    // uint64 data_sequence = 8;


    pub fn get_data_sequence(&self) -> u64 {
        self.data_sequence
    }
    pub fn clear_data_sequence(&mut self) {
        self.data_sequence = 0;
    }

    // Param is passed by value, moved
    pub fn set_data_sequence(&mut self, v: u64) {
        self.data_sequence = v;
    }

    // repeated .enginepb.BlobCreate BlobCreates = 9;


    pub fn get_blob_creates(&self) -> &[BlobCreate] {
        &self.blob_creates
    }
    pub fn clear_blob_creates(&mut self) {
        self.blob_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_blob_creates(&mut self, v: ::protobuf::RepeatedField<BlobCreate>) {
        self.blob_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_blob_creates(&mut self) -> &mut ::protobuf::RepeatedField<BlobCreate> {
        &mut self.blob_creates
    }

    // Take field
    pub fn take_blob_creates(&mut self) -> ::protobuf::RepeatedField<BlobCreate> {
        ::std::mem::replace(&mut self.blob_creates, ::protobuf::RepeatedField::new())
    }

    // uint64 max_ts = 10;


    pub fn get_max_ts(&self) -> u64 {
        self.max_ts
    }
    pub fn clear_max_ts(&mut self) {
        self.max_ts = 0;
    }

    // Param is passed by value, moved
    pub fn set_max_ts(&mut self, v: u64) {
        self.max_ts = v;
    }

    // uint32 inner_key_off = 11;


    pub fn get_inner_key_off(&self) -> u32 {
        self.inner_key_off
    }
    pub fn clear_inner_key_off(&mut self) {
        self.inner_key_off = 0;
    }

    // Param is passed by value, moved
    pub fn set_inner_key_off(&mut self, v: u32) {
        self.inner_key_off = v;
    }

    // repeated .enginepb.ColumnarCreate columnarCreates = 12;


    pub fn get_columnar_creates(&self) -> &[ColumnarCreate] {
        &self.columnar_creates
    }
    pub fn clear_columnar_creates(&mut self) {
        self.columnar_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_columnar_creates(&mut self, v: ::protobuf::RepeatedField<ColumnarCreate>) {
        self.columnar_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_columnar_creates(&mut self) -> &mut ::protobuf::RepeatedField<ColumnarCreate> {
        &mut self.columnar_creates
    }

    // Take field
    pub fn take_columnar_creates(&mut self) -> ::protobuf::RepeatedField<ColumnarCreate> {
        ::std::mem::replace(&mut self.columnar_creates, ::protobuf::RepeatedField::new())
    }

    // .enginepb.SchemaMeta schema_meta = 13;


    pub fn get_schema_meta(&self) -> &SchemaMeta {
        self.schema_meta.as_ref().unwrap_or_else(|| SchemaMeta::default_instance())
    }
    pub fn clear_schema_meta(&mut self) {
        self.schema_meta.clear();
    }

    pub fn has_schema_meta(&self) -> bool {
        self.schema_meta.is_some()
    }

    // Param is passed by value, moved
    pub fn set_schema_meta(&mut self, v: SchemaMeta) {
        self.schema_meta = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_schema_meta(&mut self) -> &mut SchemaMeta {
        if self.schema_meta.is_none() {
            self.schema_meta.set_default();
        }
        self.schema_meta.as_mut().unwrap()
    }

    // Take field
    pub fn take_schema_meta(&mut self) -> SchemaMeta {
        self.schema_meta.take().unwrap_or_else(|| SchemaMeta::new())
    }

    // repeated uint64 unconverted_l0s = 15;


    pub fn get_unconverted_l0s(&self) -> &[u64] {
        &self.unconverted_l0s
    }
    pub fn clear_unconverted_l0s(&mut self) {
        self.unconverted_l0s.clear();
    }

    // Param is passed by value, moved
    pub fn set_unconverted_l0s(&mut self, v: ::std::vec::Vec<u64>) {
        self.unconverted_l0s = v;
    }

    // Mutable pointer to the field.
    pub fn mut_unconverted_l0s(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.unconverted_l0s
    }

    // Take field
    pub fn take_unconverted_l0s(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.unconverted_l0s, ::std::vec::Vec::new())
    }

    // repeated .enginepb.VectorIndex vector_indexes = 16;


    pub fn get_vector_indexes(&self) -> &[VectorIndex] {
        &self.vector_indexes
    }
    pub fn clear_vector_indexes(&mut self) {
        self.vector_indexes.clear();
    }

    // Param is passed by value, moved
    pub fn set_vector_indexes(&mut self, v: ::protobuf::RepeatedField<VectorIndex>) {
        self.vector_indexes = v;
    }

    // Mutable pointer to the field.
    pub fn mut_vector_indexes(&mut self) -> &mut ::protobuf::RepeatedField<VectorIndex> {
        &mut self.vector_indexes
    }

    // Take field
    pub fn take_vector_indexes(&mut self) -> ::protobuf::RepeatedField<VectorIndex> {
        ::std::mem::replace(&mut self.vector_indexes, ::protobuf::RepeatedField::new())
    }

    // repeated int64 columnar_table_ids = 17;


    pub fn get_columnar_table_ids(&self) -> &[i64] {
        &self.columnar_table_ids
    }
    pub fn clear_columnar_table_ids(&mut self) {
        self.columnar_table_ids.clear();
    }

    // Param is passed by value, moved
    pub fn set_columnar_table_ids(&mut self, v: ::std::vec::Vec<i64>) {
        self.columnar_table_ids = v;
    }

    // Mutable pointer to the field.
    pub fn mut_columnar_table_ids(&mut self) -> &mut ::std::vec::Vec<i64> {
        &mut self.columnar_table_ids
    }

    // Take field
    pub fn take_columnar_table_ids(&mut self) -> ::std::vec::Vec<i64> {
        ::std::mem::replace(&mut self.columnar_table_ids, ::std::vec::Vec::new())
    }

    // uint64 columnar_l2_snap_version = 18;


    pub fn get_columnar_l2_snap_version(&self) -> u64 {
        self.columnar_l2_snap_version
    }
    pub fn clear_columnar_l2_snap_version(&mut self) {
        self.columnar_l2_snap_version = 0;
    }

    // Param is passed by value, moved
    pub fn set_columnar_l2_snap_version(&mut self, v: u64) {
        self.columnar_l2_snap_version = v;
    }

    // repeated .enginepb.FtsL0Create fts_l0_segments = 30;


    pub fn get_fts_l0_segments(&self) -> &[FtsL0Create] {
        &self.fts_l0_segments
    }
    pub fn clear_fts_l0_segments(&mut self) {
        self.fts_l0_segments.clear();
    }

    // Param is passed by value, moved
    pub fn set_fts_l0_segments(&mut self, v: ::protobuf::RepeatedField<FtsL0Create>) {
        self.fts_l0_segments = v;
    }

    // Mutable pointer to the field.
    pub fn mut_fts_l0_segments(&mut self) -> &mut ::protobuf::RepeatedField<FtsL0Create> {
        &mut self.fts_l0_segments
    }

    // Take field
    pub fn take_fts_l0_segments(&mut self) -> ::protobuf::RepeatedField<FtsL0Create> {
        ::std::mem::replace(&mut self.fts_l0_segments, ::protobuf::RepeatedField::new())
    }

    // repeated uint64 fts_unconverted_l0s = 31;


    pub fn get_fts_unconverted_l0s(&self) -> &[u64] {
        &self.fts_unconverted_l0s
    }
    pub fn clear_fts_unconverted_l0s(&mut self) {
        self.fts_unconverted_l0s.clear();
    }

    // Param is passed by value, moved
    pub fn set_fts_unconverted_l0s(&mut self, v: ::std::vec::Vec<u64>) {
        self.fts_unconverted_l0s = v;
    }

    // Mutable pointer to the field.
    pub fn mut_fts_unconverted_l0s(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.fts_unconverted_l0s
    }

    // Take field
    pub fn take_fts_unconverted_l0s(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.fts_unconverted_l0s, ::std::vec::Vec::new())
    }
}

impl ::protobuf::Message for Snapshot {
    fn is_initialized(&self) -> bool {
        for v in &self.properties {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.l0_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.table_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.blob_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.columnar_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.schema_meta {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.vector_indexes {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.fts_l0_segments {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.outer_start)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.outer_end)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.properties)?;
                },
                5 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.l0_creates)?;
                },
                6 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.table_creates)?;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.base_version = tmp;
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.data_sequence = tmp;
                },
                9 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.blob_creates)?;
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.max_ts = tmp;
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.inner_key_off = tmp;
                },
                12 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.columnar_creates)?;
                },
                13 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.schema_meta)?;
                },
                15 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.unconverted_l0s)?;
                },
                16 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.vector_indexes)?;
                },
                17 => {
                    ::protobuf::rt::read_repeated_int64_into(wire_type, is, &mut self.columnar_table_ids)?;
                },
                18 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.columnar_l2_snap_version = tmp;
                },
                30 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.fts_l0_segments)?;
                },
                31 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.fts_unconverted_l0s)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.outer_start.is_empty() {
            my_size += ::protobuf::rt::bytes_size(1, &self.outer_start);
        }
        if !self.outer_end.is_empty() {
            my_size += ::protobuf::rt::bytes_size(2, &self.outer_end);
        }
        if let Some(ref v) = self.properties.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        for value in &self.l0_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.table_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if self.base_version != 0 {
            my_size += ::protobuf::rt::value_size(7, self.base_version, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.data_sequence != 0 {
            my_size += ::protobuf::rt::value_size(8, self.data_sequence, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.blob_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if self.max_ts != 0 {
            my_size += ::protobuf::rt::value_size(10, self.max_ts, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.inner_key_off != 0 {
            my_size += ::protobuf::rt::value_size(11, self.inner_key_off, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.columnar_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if let Some(ref v) = self.schema_meta.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        for value in &self.unconverted_l0s {
            my_size += ::protobuf::rt::value_size(15, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        for value in &self.vector_indexes {
            let len = value.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.columnar_table_ids {
            my_size += ::protobuf::rt::value_size(17, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        if self.columnar_l2_snap_version != 0 {
            my_size += ::protobuf::rt::value_size(18, self.columnar_l2_snap_version, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.fts_l0_segments {
            let len = value.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.fts_unconverted_l0s {
            my_size += ::protobuf::rt::value_size(31, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if !self.outer_start.is_empty() {
            os.write_bytes(1, &self.outer_start)?;
        }
        if !self.outer_end.is_empty() {
            os.write_bytes(2, &self.outer_end)?;
        }
        if let Some(ref v) = self.properties.as_ref() {
            os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        for v in &self.l0_creates {
            os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.table_creates {
            os.write_tag(6, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if self.base_version != 0 {
            os.write_uint64(7, self.base_version)?;
        }
        if self.data_sequence != 0 {
            os.write_uint64(8, self.data_sequence)?;
        }
        for v in &self.blob_creates {
            os.write_tag(9, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if self.max_ts != 0 {
            os.write_uint64(10, self.max_ts)?;
        }
        if self.inner_key_off != 0 {
            os.write_uint32(11, self.inner_key_off)?;
        }
        for v in &self.columnar_creates {
            os.write_tag(12, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if let Some(ref v) = self.schema_meta.as_ref() {
            os.write_tag(13, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        for v in &self.unconverted_l0s {
            os.write_uint64(15, *v)?;
        };
        for v in &self.vector_indexes {
            os.write_tag(16, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.columnar_table_ids {
            os.write_int64(17, *v)?;
        };
        if self.columnar_l2_snap_version != 0 {
            os.write_uint64(18, self.columnar_l2_snap_version)?;
        }
        for v in &self.fts_l0_segments {
            os.write_tag(30, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.fts_unconverted_l0s {
            os.write_uint64(31, *v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Snapshot {
        Snapshot::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "outer_start",
                    |m: &Snapshot| { &m.outer_start },
                    |m: &mut Snapshot| { &mut m.outer_start },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "outer_end",
                    |m: &Snapshot| { &m.outer_end },
                    |m: &mut Snapshot| { &mut m.outer_end },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Properties>>(
                    "properties",
                    |m: &Snapshot| { &m.properties },
                    |m: &mut Snapshot| { &mut m.properties },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<L0Create>>(
                    "l0Creates",
                    |m: &Snapshot| { &m.l0_creates },
                    |m: &mut Snapshot| { &mut m.l0_creates },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableCreate>>(
                    "tableCreates",
                    |m: &Snapshot| { &m.table_creates },
                    |m: &mut Snapshot| { &mut m.table_creates },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "baseVersion",
                    |m: &Snapshot| { &m.base_version },
                    |m: &mut Snapshot| { &mut m.base_version },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "data_sequence",
                    |m: &Snapshot| { &m.data_sequence },
                    |m: &mut Snapshot| { &mut m.data_sequence },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<BlobCreate>>(
                    "BlobCreates",
                    |m: &Snapshot| { &m.blob_creates },
                    |m: &mut Snapshot| { &mut m.blob_creates },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "max_ts",
                    |m: &Snapshot| { &m.max_ts },
                    |m: &mut Snapshot| { &mut m.max_ts },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "inner_key_off",
                    |m: &Snapshot| { &m.inner_key_off },
                    |m: &mut Snapshot| { &mut m.inner_key_off },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ColumnarCreate>>(
                    "columnarCreates",
                    |m: &Snapshot| { &m.columnar_creates },
                    |m: &mut Snapshot| { &mut m.columnar_creates },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<SchemaMeta>>(
                    "schema_meta",
                    |m: &Snapshot| { &m.schema_meta },
                    |m: &mut Snapshot| { &mut m.schema_meta },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "unconverted_l0s",
                    |m: &Snapshot| { &m.unconverted_l0s },
                    |m: &mut Snapshot| { &mut m.unconverted_l0s },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<VectorIndex>>(
                    "vector_indexes",
                    |m: &Snapshot| { &m.vector_indexes },
                    |m: &mut Snapshot| { &mut m.vector_indexes },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "columnar_table_ids",
                    |m: &Snapshot| { &m.columnar_table_ids },
                    |m: &mut Snapshot| { &mut m.columnar_table_ids },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "columnar_l2_snap_version",
                    |m: &Snapshot| { &m.columnar_l2_snap_version },
                    |m: &mut Snapshot| { &mut m.columnar_l2_snap_version },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<FtsL0Create>>(
                    "fts_l0_segments",
                    |m: &Snapshot| { &m.fts_l0_segments },
                    |m: &mut Snapshot| { &mut m.fts_l0_segments },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "fts_unconverted_l0s",
                    |m: &Snapshot| { &m.fts_unconverted_l0s },
                    |m: &mut Snapshot| { &mut m.fts_unconverted_l0s },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<Snapshot>(
                    "Snapshot",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static Snapshot {
        static mut instance: ::protobuf::lazy::Lazy<Snapshot> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const Snapshot,
        };
        unsafe {
            instance.get(Snapshot::new)
        }
    }
}

impl ::protobuf::Clear for Snapshot {
    fn clear(&mut self) {
        self.outer_start.clear();
        self.outer_end.clear();
        self.properties.clear();
        self.l0_creates.clear();
        self.table_creates.clear();
        self.base_version = 0;
        self.data_sequence = 0;
        self.blob_creates.clear();
        self.max_ts = 0;
        self.inner_key_off = 0;
        self.columnar_creates.clear();
        self.schema_meta.clear();
        self.unconverted_l0s.clear();
        self.vector_indexes.clear();
        self.columnar_table_ids.clear();
        self.columnar_l2_snap_version = 0;
        self.fts_l0_segments.clear();
        self.fts_unconverted_l0s.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for Snapshot {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.outer_start, "outer_start", buf);
        ::protobuf::PbPrint::fmt(&self.outer_end, "outer_end", buf);
        ::protobuf::PbPrint::fmt(&self.properties, "properties", buf);
        ::protobuf::PbPrint::fmt(&self.l0_creates, "l0_creates", buf);
        ::protobuf::PbPrint::fmt(&self.table_creates, "table_creates", buf);
        ::protobuf::PbPrint::fmt(&self.base_version, "base_version", buf);
        ::protobuf::PbPrint::fmt(&self.data_sequence, "data_sequence", buf);
        ::protobuf::PbPrint::fmt(&self.blob_creates, "blob_creates", buf);
        ::protobuf::PbPrint::fmt(&self.max_ts, "max_ts", buf);
        ::protobuf::PbPrint::fmt(&self.inner_key_off, "inner_key_off", buf);
        ::protobuf::PbPrint::fmt(&self.columnar_creates, "columnar_creates", buf);
        ::protobuf::PbPrint::fmt(&self.schema_meta, "schema_meta", buf);
        ::protobuf::PbPrint::fmt(&self.unconverted_l0s, "unconverted_l0s", buf);
        ::protobuf::PbPrint::fmt(&self.vector_indexes, "vector_indexes", buf);
        ::protobuf::PbPrint::fmt(&self.columnar_table_ids, "columnar_table_ids", buf);
        ::protobuf::PbPrint::fmt(&self.columnar_l2_snap_version, "columnar_l2_snap_version", buf);
        ::protobuf::PbPrint::fmt(&self.fts_l0_segments, "fts_l0_segments", buf);
        ::protobuf::PbPrint::fmt(&self.fts_unconverted_l0s, "fts_unconverted_l0s", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for Snapshot {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.outer_start, "outer_start", &mut s);
        ::protobuf::PbPrint::fmt(&self.outer_end, "outer_end", &mut s);
        ::protobuf::PbPrint::fmt(&self.properties, "properties", &mut s);
        ::protobuf::PbPrint::fmt(&self.l0_creates, "l0_creates", &mut s);
        ::protobuf::PbPrint::fmt(&self.table_creates, "table_creates", &mut s);
        ::protobuf::PbPrint::fmt(&self.base_version, "base_version", &mut s);
        ::protobuf::PbPrint::fmt(&self.data_sequence, "data_sequence", &mut s);
        ::protobuf::PbPrint::fmt(&self.blob_creates, "blob_creates", &mut s);
        ::protobuf::PbPrint::fmt(&self.max_ts, "max_ts", &mut s);
        ::protobuf::PbPrint::fmt(&self.inner_key_off, "inner_key_off", &mut s);
        ::protobuf::PbPrint::fmt(&self.columnar_creates, "columnar_creates", &mut s);
        ::protobuf::PbPrint::fmt(&self.schema_meta, "schema_meta", &mut s);
        ::protobuf::PbPrint::fmt(&self.unconverted_l0s, "unconverted_l0s", &mut s);
        ::protobuf::PbPrint::fmt(&self.vector_indexes, "vector_indexes", &mut s);
        ::protobuf::PbPrint::fmt(&self.columnar_table_ids, "columnar_table_ids", &mut s);
        ::protobuf::PbPrint::fmt(&self.columnar_l2_snap_version, "columnar_l2_snap_version", &mut s);
        ::protobuf::PbPrint::fmt(&self.fts_l0_segments, "fts_l0_segments", &mut s);
        ::protobuf::PbPrint::fmt(&self.fts_unconverted_l0s, "fts_unconverted_l0s", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for Snapshot {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct L0Create {
    // message fields
    pub id: u64,
    pub smallest: ::std::vec::Vec<u8>,
    pub biggest: ::std::vec::Vec<u8>,
    pub size: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a L0Create {
    fn default() -> &'a L0Create {
        <L0Create as ::protobuf::Message>::default_instance()
    }
}

impl L0Create {
    pub fn new() -> L0Create {
        ::std::default::Default::default()
    }

    // uint64 ID = 1;


    pub fn get_id(&self) -> u64 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: u64) {
        self.id = v;
    }

    // bytes smallest = 2;


    pub fn get_smallest(&self) -> &[u8] {
        &self.smallest
    }
    pub fn clear_smallest(&mut self) {
        self.smallest.clear();
    }

    // Param is passed by value, moved
    pub fn set_smallest(&mut self, v: ::std::vec::Vec<u8>) {
        self.smallest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_smallest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.smallest
    }

    // Take field
    pub fn take_smallest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.smallest, ::std::vec::Vec::new())
    }

    // bytes biggest = 3;


    pub fn get_biggest(&self) -> &[u8] {
        &self.biggest
    }
    pub fn clear_biggest(&mut self) {
        self.biggest.clear();
    }

    // Param is passed by value, moved
    pub fn set_biggest(&mut self, v: ::std::vec::Vec<u8>) {
        self.biggest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_biggest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.biggest
    }

    // Take field
    pub fn take_biggest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.biggest, ::std::vec::Vec::new())
    }

    // uint32 size = 4;


    pub fn get_size(&self) -> u32 {
        self.size
    }
    pub fn clear_size(&mut self) {
        self.size = 0;
    }

    // Param is passed by value, moved
    pub fn set_size(&mut self, v: u32) {
        self.size = v;
    }
}

impl ::protobuf::Message for L0Create {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.smallest)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.biggest)?;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.size = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.smallest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(2, &self.smallest);
        }
        if !self.biggest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(3, &self.biggest);
        }
        if self.size != 0 {
            my_size += ::protobuf::rt::value_size(4, self.size, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_uint64(1, self.id)?;
        }
        if !self.smallest.is_empty() {
            os.write_bytes(2, &self.smallest)?;
        }
        if !self.biggest.is_empty() {
            os.write_bytes(3, &self.biggest)?;
        }
        if self.size != 0 {
            os.write_uint32(4, self.size)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> L0Create {
        L0Create::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "ID",
                    |m: &L0Create| { &m.id },
                    |m: &mut L0Create| { &mut m.id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "smallest",
                    |m: &L0Create| { &m.smallest },
                    |m: &mut L0Create| { &mut m.smallest },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "biggest",
                    |m: &L0Create| { &m.biggest },
                    |m: &mut L0Create| { &mut m.biggest },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "size",
                    |m: &L0Create| { &m.size },
                    |m: &mut L0Create| { &mut m.size },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<L0Create>(
                    "L0Create",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static L0Create {
        static mut instance: ::protobuf::lazy::Lazy<L0Create> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const L0Create,
        };
        unsafe {
            instance.get(L0Create::new)
        }
    }
}

impl ::protobuf::Clear for L0Create {
    fn clear(&mut self) {
        self.id = 0;
        self.smallest.clear();
        self.biggest.clear();
        self.size = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for L0Create {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.id, "id", buf);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", buf);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", buf);
        ::protobuf::PbPrint::fmt(&self.size, "size", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for L0Create {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.id, "id", &mut s);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", &mut s);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", &mut s);
        ::protobuf::PbPrint::fmt(&self.size, "size", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for L0Create {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct BlobCreate {
    // message fields
    pub id: u64,
    pub smallest: ::std::vec::Vec<u8>,
    pub biggest: ::std::vec::Vec<u8>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a BlobCreate {
    fn default() -> &'a BlobCreate {
        <BlobCreate as ::protobuf::Message>::default_instance()
    }
}

impl BlobCreate {
    pub fn new() -> BlobCreate {
        ::std::default::Default::default()
    }

    // uint64 ID = 1;


    pub fn get_id(&self) -> u64 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: u64) {
        self.id = v;
    }

    // bytes smallest = 2;


    pub fn get_smallest(&self) -> &[u8] {
        &self.smallest
    }
    pub fn clear_smallest(&mut self) {
        self.smallest.clear();
    }

    // Param is passed by value, moved
    pub fn set_smallest(&mut self, v: ::std::vec::Vec<u8>) {
        self.smallest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_smallest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.smallest
    }

    // Take field
    pub fn take_smallest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.smallest, ::std::vec::Vec::new())
    }

    // bytes biggest = 3;


    pub fn get_biggest(&self) -> &[u8] {
        &self.biggest
    }
    pub fn clear_biggest(&mut self) {
        self.biggest.clear();
    }

    // Param is passed by value, moved
    pub fn set_biggest(&mut self, v: ::std::vec::Vec<u8>) {
        self.biggest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_biggest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.biggest
    }

    // Take field
    pub fn take_biggest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.biggest, ::std::vec::Vec::new())
    }
}

impl ::protobuf::Message for BlobCreate {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.smallest)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.biggest)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.smallest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(2, &self.smallest);
        }
        if !self.biggest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(3, &self.biggest);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_uint64(1, self.id)?;
        }
        if !self.smallest.is_empty() {
            os.write_bytes(2, &self.smallest)?;
        }
        if !self.biggest.is_empty() {
            os.write_bytes(3, &self.biggest)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> BlobCreate {
        BlobCreate::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "ID",
                    |m: &BlobCreate| { &m.id },
                    |m: &mut BlobCreate| { &mut m.id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "smallest",
                    |m: &BlobCreate| { &m.smallest },
                    |m: &mut BlobCreate| { &mut m.smallest },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "biggest",
                    |m: &BlobCreate| { &m.biggest },
                    |m: &mut BlobCreate| { &mut m.biggest },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<BlobCreate>(
                    "BlobCreate",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static BlobCreate {
        static mut instance: ::protobuf::lazy::Lazy<BlobCreate> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const BlobCreate,
        };
        unsafe {
            instance.get(BlobCreate::new)
        }
    }
}

impl ::protobuf::Clear for BlobCreate {
    fn clear(&mut self) {
        self.id = 0;
        self.smallest.clear();
        self.biggest.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for BlobCreate {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.id, "id", buf);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", buf);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for BlobCreate {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.id, "id", &mut s);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", &mut s);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for BlobCreate {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct TableCreate {
    // message fields
    pub id: u64,
    pub level: u32,
    pub cf: i32,
    pub smallest: ::std::vec::Vec<u8>,
    pub biggest: ::std::vec::Vec<u8>,
    pub meta_offset: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a TableCreate {
    fn default() -> &'a TableCreate {
        <TableCreate as ::protobuf::Message>::default_instance()
    }
}

impl TableCreate {
    pub fn new() -> TableCreate {
        ::std::default::Default::default()
    }

    // uint64 ID = 1;


    pub fn get_id(&self) -> u64 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: u64) {
        self.id = v;
    }

    // uint32 level = 2;


    pub fn get_level(&self) -> u32 {
        self.level
    }
    pub fn clear_level(&mut self) {
        self.level = 0;
    }

    // Param is passed by value, moved
    pub fn set_level(&mut self, v: u32) {
        self.level = v;
    }

    // int32 CF = 3;


    pub fn get_cf(&self) -> i32 {
        self.cf
    }
    pub fn clear_cf(&mut self) {
        self.cf = 0;
    }

    // Param is passed by value, moved
    pub fn set_cf(&mut self, v: i32) {
        self.cf = v;
    }

    // bytes smallest = 4;


    pub fn get_smallest(&self) -> &[u8] {
        &self.smallest
    }
    pub fn clear_smallest(&mut self) {
        self.smallest.clear();
    }

    // Param is passed by value, moved
    pub fn set_smallest(&mut self, v: ::std::vec::Vec<u8>) {
        self.smallest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_smallest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.smallest
    }

    // Take field
    pub fn take_smallest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.smallest, ::std::vec::Vec::new())
    }

    // bytes biggest = 5;


    pub fn get_biggest(&self) -> &[u8] {
        &self.biggest
    }
    pub fn clear_biggest(&mut self) {
        self.biggest.clear();
    }

    // Param is passed by value, moved
    pub fn set_biggest(&mut self, v: ::std::vec::Vec<u8>) {
        self.biggest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_biggest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.biggest
    }

    // Take field
    pub fn take_biggest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.biggest, ::std::vec::Vec::new())
    }

    // uint32 meta_offset = 7;


    pub fn get_meta_offset(&self) -> u32 {
        self.meta_offset
    }
    pub fn clear_meta_offset(&mut self) {
        self.meta_offset = 0;
    }

    // Param is passed by value, moved
    pub fn set_meta_offset(&mut self, v: u32) {
        self.meta_offset = v;
    }
}

impl ::protobuf::Message for TableCreate {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.level = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.cf = tmp;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.smallest)?;
                },
                5 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.biggest)?;
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.meta_offset = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.level != 0 {
            my_size += ::protobuf::rt::value_size(2, self.level, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.cf != 0 {
            my_size += ::protobuf::rt::value_size(3, self.cf, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.smallest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(4, &self.smallest);
        }
        if !self.biggest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(5, &self.biggest);
        }
        if self.meta_offset != 0 {
            my_size += ::protobuf::rt::value_size(7, self.meta_offset, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_uint64(1, self.id)?;
        }
        if self.level != 0 {
            os.write_uint32(2, self.level)?;
        }
        if self.cf != 0 {
            os.write_int32(3, self.cf)?;
        }
        if !self.smallest.is_empty() {
            os.write_bytes(4, &self.smallest)?;
        }
        if !self.biggest.is_empty() {
            os.write_bytes(5, &self.biggest)?;
        }
        if self.meta_offset != 0 {
            os.write_uint32(7, self.meta_offset)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> TableCreate {
        TableCreate::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "ID",
                    |m: &TableCreate| { &m.id },
                    |m: &mut TableCreate| { &mut m.id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "level",
                    |m: &TableCreate| { &m.level },
                    |m: &mut TableCreate| { &mut m.level },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                    "CF",
                    |m: &TableCreate| { &m.cf },
                    |m: &mut TableCreate| { &mut m.cf },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "smallest",
                    |m: &TableCreate| { &m.smallest },
                    |m: &mut TableCreate| { &mut m.smallest },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "biggest",
                    |m: &TableCreate| { &m.biggest },
                    |m: &mut TableCreate| { &mut m.biggest },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "meta_offset",
                    |m: &TableCreate| { &m.meta_offset },
                    |m: &mut TableCreate| { &mut m.meta_offset },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<TableCreate>(
                    "TableCreate",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static TableCreate {
        static mut instance: ::protobuf::lazy::Lazy<TableCreate> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const TableCreate,
        };
        unsafe {
            instance.get(TableCreate::new)
        }
    }
}

impl ::protobuf::Clear for TableCreate {
    fn clear(&mut self) {
        self.id = 0;
        self.level = 0;
        self.cf = 0;
        self.smallest.clear();
        self.biggest.clear();
        self.meta_offset = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for TableCreate {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.id, "id", buf);
        ::protobuf::PbPrint::fmt(&self.level, "level", buf);
        ::protobuf::PbPrint::fmt(&self.cf, "cf", buf);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", buf);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", buf);
        ::protobuf::PbPrint::fmt(&self.meta_offset, "meta_offset", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for TableCreate {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.id, "id", &mut s);
        ::protobuf::PbPrint::fmt(&self.level, "level", &mut s);
        ::protobuf::PbPrint::fmt(&self.cf, "cf", &mut s);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", &mut s);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", &mut s);
        ::protobuf::PbPrint::fmt(&self.meta_offset, "meta_offset", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for TableCreate {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct TableDelete {
    // message fields
    pub id: u64,
    pub level: u32,
    pub cf: i32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a TableDelete {
    fn default() -> &'a TableDelete {
        <TableDelete as ::protobuf::Message>::default_instance()
    }
}

impl TableDelete {
    pub fn new() -> TableDelete {
        ::std::default::Default::default()
    }

    // uint64 ID = 1;


    pub fn get_id(&self) -> u64 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: u64) {
        self.id = v;
    }

    // uint32 level = 2;


    pub fn get_level(&self) -> u32 {
        self.level
    }
    pub fn clear_level(&mut self) {
        self.level = 0;
    }

    // Param is passed by value, moved
    pub fn set_level(&mut self, v: u32) {
        self.level = v;
    }

    // int32 CF = 3;


    pub fn get_cf(&self) -> i32 {
        self.cf
    }
    pub fn clear_cf(&mut self) {
        self.cf = 0;
    }

    // Param is passed by value, moved
    pub fn set_cf(&mut self, v: i32) {
        self.cf = v;
    }
}

impl ::protobuf::Message for TableDelete {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.level = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int32()?;
                    self.cf = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.level != 0 {
            my_size += ::protobuf::rt::value_size(2, self.level, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.cf != 0 {
            my_size += ::protobuf::rt::value_size(3, self.cf, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_uint64(1, self.id)?;
        }
        if self.level != 0 {
            os.write_uint32(2, self.level)?;
        }
        if self.cf != 0 {
            os.write_int32(3, self.cf)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> TableDelete {
        TableDelete::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "ID",
                    |m: &TableDelete| { &m.id },
                    |m: &mut TableDelete| { &mut m.id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "level",
                    |m: &TableDelete| { &m.level },
                    |m: &mut TableDelete| { &mut m.level },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt32>(
                    "CF",
                    |m: &TableDelete| { &m.cf },
                    |m: &mut TableDelete| { &mut m.cf },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<TableDelete>(
                    "TableDelete",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static TableDelete {
        static mut instance: ::protobuf::lazy::Lazy<TableDelete> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const TableDelete,
        };
        unsafe {
            instance.get(TableDelete::new)
        }
    }
}

impl ::protobuf::Clear for TableDelete {
    fn clear(&mut self) {
        self.id = 0;
        self.level = 0;
        self.cf = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for TableDelete {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.id, "id", buf);
        ::protobuf::PbPrint::fmt(&self.level, "level", buf);
        ::protobuf::PbPrint::fmt(&self.cf, "cf", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for TableDelete {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.id, "id", &mut s);
        ::protobuf::PbPrint::fmt(&self.level, "level", &mut s);
        ::protobuf::PbPrint::fmt(&self.cf, "cf", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for TableDelete {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ColumnarCreate {
    // message fields
    pub id: u64,
    pub level: u32,
    pub smallest: ::std::vec::Vec<u8>,
    pub biggest: ::std::vec::Vec<u8>,
    pub meta_offset: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ColumnarCreate {
    fn default() -> &'a ColumnarCreate {
        <ColumnarCreate as ::protobuf::Message>::default_instance()
    }
}

impl ColumnarCreate {
    pub fn new() -> ColumnarCreate {
        ::std::default::Default::default()
    }

    // uint64 ID = 1;


    pub fn get_id(&self) -> u64 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: u64) {
        self.id = v;
    }

    // uint32 level = 2;


    pub fn get_level(&self) -> u32 {
        self.level
    }
    pub fn clear_level(&mut self) {
        self.level = 0;
    }

    // Param is passed by value, moved
    pub fn set_level(&mut self, v: u32) {
        self.level = v;
    }

    // bytes smallest = 3;


    pub fn get_smallest(&self) -> &[u8] {
        &self.smallest
    }
    pub fn clear_smallest(&mut self) {
        self.smallest.clear();
    }

    // Param is passed by value, moved
    pub fn set_smallest(&mut self, v: ::std::vec::Vec<u8>) {
        self.smallest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_smallest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.smallest
    }

    // Take field
    pub fn take_smallest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.smallest, ::std::vec::Vec::new())
    }

    // bytes biggest = 4;


    pub fn get_biggest(&self) -> &[u8] {
        &self.biggest
    }
    pub fn clear_biggest(&mut self) {
        self.biggest.clear();
    }

    // Param is passed by value, moved
    pub fn set_biggest(&mut self, v: ::std::vec::Vec<u8>) {
        self.biggest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_biggest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.biggest
    }

    // Take field
    pub fn take_biggest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.biggest, ::std::vec::Vec::new())
    }

    // uint32 meta_offset = 5;


    pub fn get_meta_offset(&self) -> u32 {
        self.meta_offset
    }
    pub fn clear_meta_offset(&mut self) {
        self.meta_offset = 0;
    }

    // Param is passed by value, moved
    pub fn set_meta_offset(&mut self, v: u32) {
        self.meta_offset = v;
    }
}

impl ::protobuf::Message for ColumnarCreate {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.level = tmp;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.smallest)?;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.biggest)?;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.meta_offset = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.level != 0 {
            my_size += ::protobuf::rt::value_size(2, self.level, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.smallest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(3, &self.smallest);
        }
        if !self.biggest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(4, &self.biggest);
        }
        if self.meta_offset != 0 {
            my_size += ::protobuf::rt::value_size(5, self.meta_offset, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_uint64(1, self.id)?;
        }
        if self.level != 0 {
            os.write_uint32(2, self.level)?;
        }
        if !self.smallest.is_empty() {
            os.write_bytes(3, &self.smallest)?;
        }
        if !self.biggest.is_empty() {
            os.write_bytes(4, &self.biggest)?;
        }
        if self.meta_offset != 0 {
            os.write_uint32(5, self.meta_offset)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ColumnarCreate {
        ColumnarCreate::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "ID",
                    |m: &ColumnarCreate| { &m.id },
                    |m: &mut ColumnarCreate| { &mut m.id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "level",
                    |m: &ColumnarCreate| { &m.level },
                    |m: &mut ColumnarCreate| { &mut m.level },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "smallest",
                    |m: &ColumnarCreate| { &m.smallest },
                    |m: &mut ColumnarCreate| { &mut m.smallest },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "biggest",
                    |m: &ColumnarCreate| { &m.biggest },
                    |m: &mut ColumnarCreate| { &mut m.biggest },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "meta_offset",
                    |m: &ColumnarCreate| { &m.meta_offset },
                    |m: &mut ColumnarCreate| { &mut m.meta_offset },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<ColumnarCreate>(
                    "ColumnarCreate",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static ColumnarCreate {
        static mut instance: ::protobuf::lazy::Lazy<ColumnarCreate> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ColumnarCreate,
        };
        unsafe {
            instance.get(ColumnarCreate::new)
        }
    }
}

impl ::protobuf::Clear for ColumnarCreate {
    fn clear(&mut self) {
        self.id = 0;
        self.level = 0;
        self.smallest.clear();
        self.biggest.clear();
        self.meta_offset = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for ColumnarCreate {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.id, "id", buf);
        ::protobuf::PbPrint::fmt(&self.level, "level", buf);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", buf);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", buf);
        ::protobuf::PbPrint::fmt(&self.meta_offset, "meta_offset", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for ColumnarCreate {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.id, "id", &mut s);
        ::protobuf::PbPrint::fmt(&self.level, "level", &mut s);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", &mut s);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", &mut s);
        ::protobuf::PbPrint::fmt(&self.meta_offset, "meta_offset", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for ColumnarCreate {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ColumnarDelete {
    // message fields
    pub id: u64,
    pub level: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ColumnarDelete {
    fn default() -> &'a ColumnarDelete {
        <ColumnarDelete as ::protobuf::Message>::default_instance()
    }
}

impl ColumnarDelete {
    pub fn new() -> ColumnarDelete {
        ::std::default::Default::default()
    }

    // uint64 ID = 1;


    pub fn get_id(&self) -> u64 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: u64) {
        self.id = v;
    }

    // uint32 level = 2;


    pub fn get_level(&self) -> u32 {
        self.level
    }
    pub fn clear_level(&mut self) {
        self.level = 0;
    }

    // Param is passed by value, moved
    pub fn set_level(&mut self, v: u32) {
        self.level = v;
    }
}

impl ::protobuf::Message for ColumnarDelete {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.level = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.level != 0 {
            my_size += ::protobuf::rt::value_size(2, self.level, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_uint64(1, self.id)?;
        }
        if self.level != 0 {
            os.write_uint32(2, self.level)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ColumnarDelete {
        ColumnarDelete::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "ID",
                    |m: &ColumnarDelete| { &m.id },
                    |m: &mut ColumnarDelete| { &mut m.id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "level",
                    |m: &ColumnarDelete| { &m.level },
                    |m: &mut ColumnarDelete| { &mut m.level },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<ColumnarDelete>(
                    "ColumnarDelete",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static ColumnarDelete {
        static mut instance: ::protobuf::lazy::Lazy<ColumnarDelete> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ColumnarDelete,
        };
        unsafe {
            instance.get(ColumnarDelete::new)
        }
    }
}

impl ::protobuf::Clear for ColumnarDelete {
    fn clear(&mut self) {
        self.id = 0;
        self.level = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for ColumnarDelete {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.id, "id", buf);
        ::protobuf::PbPrint::fmt(&self.level, "level", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for ColumnarDelete {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.id, "id", &mut s);
        ::protobuf::PbPrint::fmt(&self.level, "level", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for ColumnarDelete {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct Split {
    // message fields
    pub new_shards: ::protobuf::RepeatedField<Properties>,
    pub keys: ::protobuf::RepeatedField<::std::vec::Vec<u8>>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Split {
    fn default() -> &'a Split {
        <Split as ::protobuf::Message>::default_instance()
    }
}

impl Split {
    pub fn new() -> Split {
        ::std::default::Default::default()
    }

    // repeated .enginepb.Properties newShards = 1;


    pub fn get_new_shards(&self) -> &[Properties] {
        &self.new_shards
    }
    pub fn clear_new_shards(&mut self) {
        self.new_shards.clear();
    }

    // Param is passed by value, moved
    pub fn set_new_shards(&mut self, v: ::protobuf::RepeatedField<Properties>) {
        self.new_shards = v;
    }

    // Mutable pointer to the field.
    pub fn mut_new_shards(&mut self) -> &mut ::protobuf::RepeatedField<Properties> {
        &mut self.new_shards
    }

    // Take field
    pub fn take_new_shards(&mut self) -> ::protobuf::RepeatedField<Properties> {
        ::std::mem::replace(&mut self.new_shards, ::protobuf::RepeatedField::new())
    }

    // repeated bytes Keys = 3;


    pub fn get_keys(&self) -> &[::std::vec::Vec<u8>] {
        &self.keys
    }
    pub fn clear_keys(&mut self) {
        self.keys.clear();
    }

    // Param is passed by value, moved
    pub fn set_keys(&mut self, v: ::protobuf::RepeatedField<::std::vec::Vec<u8>>) {
        self.keys = v;
    }

    // Mutable pointer to the field.
    pub fn mut_keys(&mut self) -> &mut ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        &mut self.keys
    }

    // Take field
    pub fn take_keys(&mut self) -> ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.keys, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for Split {
    fn is_initialized(&self) -> bool {
        for v in &self.new_shards {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.new_shards)?;
                },
                3 => {
                    ::protobuf::rt::read_repeated_bytes_into(wire_type, is, &mut self.keys)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.new_shards {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.keys {
            my_size += ::protobuf::rt::bytes_size(3, &value);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        for v in &self.new_shards {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.keys {
            os.write_bytes(3, &v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Split {
        Split::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Properties>>(
                    "newShards",
                    |m: &Split| { &m.new_shards },
                    |m: &mut Split| { &mut m.new_shards },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "Keys",
                    |m: &Split| { &m.keys },
                    |m: &mut Split| { &mut m.keys },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<Split>(
                    "Split",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static Split {
        static mut instance: ::protobuf::lazy::Lazy<Split> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const Split,
        };
        unsafe {
            instance.get(Split::new)
        }
    }
}

impl ::protobuf::Clear for Split {
    fn clear(&mut self) {
        self.new_shards.clear();
        self.keys.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for Split {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.new_shards, "new_shards", buf);
        ::protobuf::PbPrint::fmt(&self.keys, "keys", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for Split {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.new_shards, "new_shards", &mut s);
        ::protobuf::PbPrint::fmt(&self.keys, "keys", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for Split {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct IngestFiles {
    // message fields
    pub l0_creates: ::protobuf::RepeatedField<L0Create>,
    pub table_creates: ::protobuf::RepeatedField<TableCreate>,
    pub properties: ::protobuf::SingularPtrField<Properties>,
    pub blob_creates: ::protobuf::RepeatedField<BlobCreate>,
    pub max_ts: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a IngestFiles {
    fn default() -> &'a IngestFiles {
        <IngestFiles as ::protobuf::Message>::default_instance()
    }
}

impl IngestFiles {
    pub fn new() -> IngestFiles {
        ::std::default::Default::default()
    }

    // repeated .enginepb.L0Create l0Creates = 1;


    pub fn get_l0_creates(&self) -> &[L0Create] {
        &self.l0_creates
    }
    pub fn clear_l0_creates(&mut self) {
        self.l0_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_l0_creates(&mut self, v: ::protobuf::RepeatedField<L0Create>) {
        self.l0_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_l0_creates(&mut self) -> &mut ::protobuf::RepeatedField<L0Create> {
        &mut self.l0_creates
    }

    // Take field
    pub fn take_l0_creates(&mut self) -> ::protobuf::RepeatedField<L0Create> {
        ::std::mem::replace(&mut self.l0_creates, ::protobuf::RepeatedField::new())
    }

    // repeated .enginepb.TableCreate tableCreates = 2;


    pub fn get_table_creates(&self) -> &[TableCreate] {
        &self.table_creates
    }
    pub fn clear_table_creates(&mut self) {
        self.table_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_table_creates(&mut self, v: ::protobuf::RepeatedField<TableCreate>) {
        self.table_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_table_creates(&mut self) -> &mut ::protobuf::RepeatedField<TableCreate> {
        &mut self.table_creates
    }

    // Take field
    pub fn take_table_creates(&mut self) -> ::protobuf::RepeatedField<TableCreate> {
        ::std::mem::replace(&mut self.table_creates, ::protobuf::RepeatedField::new())
    }

    // .enginepb.Properties properties = 3;


    pub fn get_properties(&self) -> &Properties {
        self.properties.as_ref().unwrap_or_else(|| Properties::default_instance())
    }
    pub fn clear_properties(&mut self) {
        self.properties.clear();
    }

    pub fn has_properties(&self) -> bool {
        self.properties.is_some()
    }

    // Param is passed by value, moved
    pub fn set_properties(&mut self, v: Properties) {
        self.properties = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_properties(&mut self) -> &mut Properties {
        if self.properties.is_none() {
            self.properties.set_default();
        }
        self.properties.as_mut().unwrap()
    }

    // Take field
    pub fn take_properties(&mut self) -> Properties {
        self.properties.take().unwrap_or_else(|| Properties::new())
    }

    // repeated .enginepb.BlobCreate BlobCreates = 4;


    pub fn get_blob_creates(&self) -> &[BlobCreate] {
        &self.blob_creates
    }
    pub fn clear_blob_creates(&mut self) {
        self.blob_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_blob_creates(&mut self, v: ::protobuf::RepeatedField<BlobCreate>) {
        self.blob_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_blob_creates(&mut self) -> &mut ::protobuf::RepeatedField<BlobCreate> {
        &mut self.blob_creates
    }

    // Take field
    pub fn take_blob_creates(&mut self) -> ::protobuf::RepeatedField<BlobCreate> {
        ::std::mem::replace(&mut self.blob_creates, ::protobuf::RepeatedField::new())
    }

    // uint64 max_ts = 5;


    pub fn get_max_ts(&self) -> u64 {
        self.max_ts
    }
    pub fn clear_max_ts(&mut self) {
        self.max_ts = 0;
    }

    // Param is passed by value, moved
    pub fn set_max_ts(&mut self, v: u64) {
        self.max_ts = v;
    }
}

impl ::protobuf::Message for IngestFiles {
    fn is_initialized(&self) -> bool {
        for v in &self.l0_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.table_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.properties {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.blob_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.l0_creates)?;
                },
                2 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.table_creates)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.properties)?;
                },
                4 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.blob_creates)?;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.max_ts = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.l0_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.table_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if let Some(ref v) = self.properties.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        for value in &self.blob_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if self.max_ts != 0 {
            my_size += ::protobuf::rt::value_size(5, self.max_ts, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        for v in &self.l0_creates {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.table_creates {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if let Some(ref v) = self.properties.as_ref() {
            os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        for v in &self.blob_creates {
            os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if self.max_ts != 0 {
            os.write_uint64(5, self.max_ts)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> IngestFiles {
        IngestFiles::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<L0Create>>(
                    "l0Creates",
                    |m: &IngestFiles| { &m.l0_creates },
                    |m: &mut IngestFiles| { &mut m.l0_creates },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableCreate>>(
                    "tableCreates",
                    |m: &IngestFiles| { &m.table_creates },
                    |m: &mut IngestFiles| { &mut m.table_creates },
                ));
                fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Properties>>(
                    "properties",
                    |m: &IngestFiles| { &m.properties },
                    |m: &mut IngestFiles| { &mut m.properties },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<BlobCreate>>(
                    "BlobCreates",
                    |m: &IngestFiles| { &m.blob_creates },
                    |m: &mut IngestFiles| { &mut m.blob_creates },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "max_ts",
                    |m: &IngestFiles| { &m.max_ts },
                    |m: &mut IngestFiles| { &mut m.max_ts },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<IngestFiles>(
                    "IngestFiles",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static IngestFiles {
        static mut instance: ::protobuf::lazy::Lazy<IngestFiles> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const IngestFiles,
        };
        unsafe {
            instance.get(IngestFiles::new)
        }
    }
}

impl ::protobuf::Clear for IngestFiles {
    fn clear(&mut self) {
        self.l0_creates.clear();
        self.table_creates.clear();
        self.properties.clear();
        self.blob_creates.clear();
        self.max_ts = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for IngestFiles {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.l0_creates, "l0_creates", buf);
        ::protobuf::PbPrint::fmt(&self.table_creates, "table_creates", buf);
        ::protobuf::PbPrint::fmt(&self.properties, "properties", buf);
        ::protobuf::PbPrint::fmt(&self.blob_creates, "blob_creates", buf);
        ::protobuf::PbPrint::fmt(&self.max_ts, "max_ts", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for IngestFiles {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.l0_creates, "l0_creates", &mut s);
        ::protobuf::PbPrint::fmt(&self.table_creates, "table_creates", &mut s);
        ::protobuf::PbPrint::fmt(&self.properties, "properties", &mut s);
        ::protobuf::PbPrint::fmt(&self.blob_creates, "blob_creates", &mut s);
        ::protobuf::PbPrint::fmt(&self.max_ts, "max_ts", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for IngestFiles {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct Properties {
    // message fields
    pub shard_id: u64,
    pub keys: ::protobuf::RepeatedField<::std::string::String>,
    pub values: ::protobuf::RepeatedField<::std::vec::Vec<u8>>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Properties {
    fn default() -> &'a Properties {
        <Properties as ::protobuf::Message>::default_instance()
    }
}

impl Properties {
    pub fn new() -> Properties {
        ::std::default::Default::default()
    }

    // uint64 shardID = 1;


    pub fn get_shard_id(&self) -> u64 {
        self.shard_id
    }
    pub fn clear_shard_id(&mut self) {
        self.shard_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_shard_id(&mut self, v: u64) {
        self.shard_id = v;
    }

    // repeated string keys = 2;


    pub fn get_keys(&self) -> &[::std::string::String] {
        &self.keys
    }
    pub fn clear_keys(&mut self) {
        self.keys.clear();
    }

    // Param is passed by value, moved
    pub fn set_keys(&mut self, v: ::protobuf::RepeatedField<::std::string::String>) {
        self.keys = v;
    }

    // Mutable pointer to the field.
    pub fn mut_keys(&mut self) -> &mut ::protobuf::RepeatedField<::std::string::String> {
        &mut self.keys
    }

    // Take field
    pub fn take_keys(&mut self) -> ::protobuf::RepeatedField<::std::string::String> {
        ::std::mem::replace(&mut self.keys, ::protobuf::RepeatedField::new())
    }

    // repeated bytes values = 3;


    pub fn get_values(&self) -> &[::std::vec::Vec<u8>] {
        &self.values
    }
    pub fn clear_values(&mut self) {
        self.values.clear();
    }

    // Param is passed by value, moved
    pub fn set_values(&mut self, v: ::protobuf::RepeatedField<::std::vec::Vec<u8>>) {
        self.values = v;
    }

    // Mutable pointer to the field.
    pub fn mut_values(&mut self) -> &mut ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        &mut self.values
    }

    // Take field
    pub fn take_values(&mut self) -> ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.values, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for Properties {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.shard_id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_repeated_string_into(wire_type, is, &mut self.keys)?;
                },
                3 => {
                    ::protobuf::rt::read_repeated_bytes_into(wire_type, is, &mut self.values)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.shard_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.shard_id, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.keys {
            my_size += ::protobuf::rt::string_size(2, &value);
        };
        for value in &self.values {
            my_size += ::protobuf::rt::bytes_size(3, &value);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.shard_id != 0 {
            os.write_uint64(1, self.shard_id)?;
        }
        for v in &self.keys {
            os.write_string(2, &v)?;
        };
        for v in &self.values {
            os.write_bytes(3, &v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Properties {
        Properties::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "shardID",
                    |m: &Properties| { &m.shard_id },
                    |m: &mut Properties| { &mut m.shard_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                    "keys",
                    |m: &Properties| { &m.keys },
                    |m: &mut Properties| { &mut m.keys },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "values",
                    |m: &Properties| { &m.values },
                    |m: &mut Properties| { &mut m.values },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<Properties>(
                    "Properties",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static Properties {
        static mut instance: ::protobuf::lazy::Lazy<Properties> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const Properties,
        };
        unsafe {
            instance.get(Properties::new)
        }
    }
}

impl ::protobuf::Clear for Properties {
    fn clear(&mut self) {
        self.shard_id = 0;
        self.keys.clear();
        self.values.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for Properties {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.shard_id, "shard_id", buf);
        ::protobuf::PbPrint::fmt(&self.keys, "keys", buf);
        ::protobuf::PbPrint::fmt(&self.values, "values", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for Properties {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.shard_id, "shard_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.keys, "keys", &mut s);
        ::protobuf::PbPrint::fmt(&self.values, "values", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for Properties {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct TableChange {
    // message fields
    pub table_deletes: ::protobuf::RepeatedField<TableDelete>,
    pub table_creates: ::protobuf::RepeatedField<TableCreate>,
    pub file_ids_map: ::std::vec::Vec<u64>,
    pub columnar_deletes: ::protobuf::RepeatedField<ColumnarDelete>,
    pub columnar_creates: ::protobuf::RepeatedField<ColumnarCreate>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a TableChange {
    fn default() -> &'a TableChange {
        <TableChange as ::protobuf::Message>::default_instance()
    }
}

impl TableChange {
    pub fn new() -> TableChange {
        ::std::default::Default::default()
    }

    // repeated .enginepb.TableDelete tableDeletes = 1;


    pub fn get_table_deletes(&self) -> &[TableDelete] {
        &self.table_deletes
    }
    pub fn clear_table_deletes(&mut self) {
        self.table_deletes.clear();
    }

    // Param is passed by value, moved
    pub fn set_table_deletes(&mut self, v: ::protobuf::RepeatedField<TableDelete>) {
        self.table_deletes = v;
    }

    // Mutable pointer to the field.
    pub fn mut_table_deletes(&mut self) -> &mut ::protobuf::RepeatedField<TableDelete> {
        &mut self.table_deletes
    }

    // Take field
    pub fn take_table_deletes(&mut self) -> ::protobuf::RepeatedField<TableDelete> {
        ::std::mem::replace(&mut self.table_deletes, ::protobuf::RepeatedField::new())
    }

    // repeated .enginepb.TableCreate tableCreates = 2;


    pub fn get_table_creates(&self) -> &[TableCreate] {
        &self.table_creates
    }
    pub fn clear_table_creates(&mut self) {
        self.table_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_table_creates(&mut self, v: ::protobuf::RepeatedField<TableCreate>) {
        self.table_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_table_creates(&mut self) -> &mut ::protobuf::RepeatedField<TableCreate> {
        &mut self.table_creates
    }

    // Take field
    pub fn take_table_creates(&mut self) -> ::protobuf::RepeatedField<TableCreate> {
        ::std::mem::replace(&mut self.table_creates, ::protobuf::RepeatedField::new())
    }

    // repeated uint64 file_ids_map = 3;


    pub fn get_file_ids_map(&self) -> &[u64] {
        &self.file_ids_map
    }
    pub fn clear_file_ids_map(&mut self) {
        self.file_ids_map.clear();
    }

    // Param is passed by value, moved
    pub fn set_file_ids_map(&mut self, v: ::std::vec::Vec<u64>) {
        self.file_ids_map = v;
    }

    // Mutable pointer to the field.
    pub fn mut_file_ids_map(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.file_ids_map
    }

    // Take field
    pub fn take_file_ids_map(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.file_ids_map, ::std::vec::Vec::new())
    }

    // repeated .enginepb.ColumnarDelete columnarDeletes = 4;


    pub fn get_columnar_deletes(&self) -> &[ColumnarDelete] {
        &self.columnar_deletes
    }
    pub fn clear_columnar_deletes(&mut self) {
        self.columnar_deletes.clear();
    }

    // Param is passed by value, moved
    pub fn set_columnar_deletes(&mut self, v: ::protobuf::RepeatedField<ColumnarDelete>) {
        self.columnar_deletes = v;
    }

    // Mutable pointer to the field.
    pub fn mut_columnar_deletes(&mut self) -> &mut ::protobuf::RepeatedField<ColumnarDelete> {
        &mut self.columnar_deletes
    }

    // Take field
    pub fn take_columnar_deletes(&mut self) -> ::protobuf::RepeatedField<ColumnarDelete> {
        ::std::mem::replace(&mut self.columnar_deletes, ::protobuf::RepeatedField::new())
    }

    // repeated .enginepb.ColumnarCreate columnarCreates = 5;


    pub fn get_columnar_creates(&self) -> &[ColumnarCreate] {
        &self.columnar_creates
    }
    pub fn clear_columnar_creates(&mut self) {
        self.columnar_creates.clear();
    }

    // Param is passed by value, moved
    pub fn set_columnar_creates(&mut self, v: ::protobuf::RepeatedField<ColumnarCreate>) {
        self.columnar_creates = v;
    }

    // Mutable pointer to the field.
    pub fn mut_columnar_creates(&mut self) -> &mut ::protobuf::RepeatedField<ColumnarCreate> {
        &mut self.columnar_creates
    }

    // Take field
    pub fn take_columnar_creates(&mut self) -> ::protobuf::RepeatedField<ColumnarCreate> {
        ::std::mem::replace(&mut self.columnar_creates, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for TableChange {
    fn is_initialized(&self) -> bool {
        for v in &self.table_deletes {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.table_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.columnar_deletes {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.columnar_creates {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.table_deletes)?;
                },
                2 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.table_creates)?;
                },
                3 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.file_ids_map)?;
                },
                4 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.columnar_deletes)?;
                },
                5 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.columnar_creates)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.table_deletes {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.table_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.file_ids_map {
            my_size += ::protobuf::rt::value_size(3, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        for value in &self.columnar_deletes {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.columnar_creates {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        for v in &self.table_deletes {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.table_creates {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.file_ids_map {
            os.write_uint64(3, *v)?;
        };
        for v in &self.columnar_deletes {
            os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.columnar_creates {
            os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> TableChange {
        TableChange::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableDelete>>(
                    "tableDeletes",
                    |m: &TableChange| { &m.table_deletes },
                    |m: &mut TableChange| { &mut m.table_deletes },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TableCreate>>(
                    "tableCreates",
                    |m: &TableChange| { &m.table_creates },
                    |m: &mut TableChange| { &mut m.table_creates },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "file_ids_map",
                    |m: &TableChange| { &m.file_ids_map },
                    |m: &mut TableChange| { &mut m.file_ids_map },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ColumnarDelete>>(
                    "columnarDeletes",
                    |m: &TableChange| { &m.columnar_deletes },
                    |m: &mut TableChange| { &mut m.columnar_deletes },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<ColumnarCreate>>(
                    "columnarCreates",
                    |m: &TableChange| { &m.columnar_creates },
                    |m: &mut TableChange| { &mut m.columnar_creates },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<TableChange>(
                    "TableChange",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static TableChange {
        static mut instance: ::protobuf::lazy::Lazy<TableChange> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const TableChange,
        };
        unsafe {
            instance.get(TableChange::new)
        }
    }
}

impl ::protobuf::Clear for TableChange {
    fn clear(&mut self) {
        self.table_deletes.clear();
        self.table_creates.clear();
        self.file_ids_map.clear();
        self.columnar_deletes.clear();
        self.columnar_creates.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for TableChange {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.table_deletes, "table_deletes", buf);
        ::protobuf::PbPrint::fmt(&self.table_creates, "table_creates", buf);
        ::protobuf::PbPrint::fmt(&self.file_ids_map, "file_ids_map", buf);
        ::protobuf::PbPrint::fmt(&self.columnar_deletes, "columnar_deletes", buf);
        ::protobuf::PbPrint::fmt(&self.columnar_creates, "columnar_creates", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for TableChange {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.table_deletes, "table_deletes", &mut s);
        ::protobuf::PbPrint::fmt(&self.table_creates, "table_creates", &mut s);
        ::protobuf::PbPrint::fmt(&self.file_ids_map, "file_ids_map", &mut s);
        ::protobuf::PbPrint::fmt(&self.columnar_deletes, "columnar_deletes", &mut s);
        ::protobuf::PbPrint::fmt(&self.columnar_creates, "columnar_creates", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for TableChange {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct TxnFileRefs {
    // message fields
    pub txn_file_refs: ::protobuf::RepeatedField<TxnFileRef>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a TxnFileRefs {
    fn default() -> &'a TxnFileRefs {
        <TxnFileRefs as ::protobuf::Message>::default_instance()
    }
}

impl TxnFileRefs {
    pub fn new() -> TxnFileRefs {
        ::std::default::Default::default()
    }

    // repeated .enginepb.TxnFileRef txn_file_refs = 1;


    pub fn get_txn_file_refs(&self) -> &[TxnFileRef] {
        &self.txn_file_refs
    }
    pub fn clear_txn_file_refs(&mut self) {
        self.txn_file_refs.clear();
    }

    // Param is passed by value, moved
    pub fn set_txn_file_refs(&mut self, v: ::protobuf::RepeatedField<TxnFileRef>) {
        self.txn_file_refs = v;
    }

    // Mutable pointer to the field.
    pub fn mut_txn_file_refs(&mut self) -> &mut ::protobuf::RepeatedField<TxnFileRef> {
        &mut self.txn_file_refs
    }

    // Take field
    pub fn take_txn_file_refs(&mut self) -> ::protobuf::RepeatedField<TxnFileRef> {
        ::std::mem::replace(&mut self.txn_file_refs, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for TxnFileRefs {
    fn is_initialized(&self) -> bool {
        for v in &self.txn_file_refs {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.txn_file_refs)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        for value in &self.txn_file_refs {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        for v in &self.txn_file_refs {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> TxnFileRefs {
        TxnFileRefs::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<TxnFileRef>>(
                    "txn_file_refs",
                    |m: &TxnFileRefs| { &m.txn_file_refs },
                    |m: &mut TxnFileRefs| { &mut m.txn_file_refs },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<TxnFileRefs>(
                    "TxnFileRefs",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static TxnFileRefs {
        static mut instance: ::protobuf::lazy::Lazy<TxnFileRefs> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const TxnFileRefs,
        };
        unsafe {
            instance.get(TxnFileRefs::new)
        }
    }
}

impl ::protobuf::Clear for TxnFileRefs {
    fn clear(&mut self) {
        self.txn_file_refs.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for TxnFileRefs {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.txn_file_refs, "txn_file_refs", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for TxnFileRefs {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.txn_file_refs, "txn_file_refs", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for TxnFileRefs {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct TxnFileRef {
    // message fields
    pub start_ts: u64,
    pub chunk_ids: ::std::vec::Vec<u64>,
    pub version: u64,
    pub user_meta: ::std::vec::Vec<u8>,
    pub lock_val_prefix: ::std::vec::Vec<u8>,
    pub shard_ver: u64,
    pub inner_lower_bound: ::std::vec::Vec<u8>,
    pub inner_upper_bound: ::std::vec::Vec<u8>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a TxnFileRef {
    fn default() -> &'a TxnFileRef {
        <TxnFileRef as ::protobuf::Message>::default_instance()
    }
}

impl TxnFileRef {
    pub fn new() -> TxnFileRef {
        ::std::default::Default::default()
    }

    // uint64 start_ts = 1;


    pub fn get_start_ts(&self) -> u64 {
        self.start_ts
    }
    pub fn clear_start_ts(&mut self) {
        self.start_ts = 0;
    }

    // Param is passed by value, moved
    pub fn set_start_ts(&mut self, v: u64) {
        self.start_ts = v;
    }

    // repeated uint64 chunk_ids = 2;


    pub fn get_chunk_ids(&self) -> &[u64] {
        &self.chunk_ids
    }
    pub fn clear_chunk_ids(&mut self) {
        self.chunk_ids.clear();
    }

    // Param is passed by value, moved
    pub fn set_chunk_ids(&mut self, v: ::std::vec::Vec<u64>) {
        self.chunk_ids = v;
    }

    // Mutable pointer to the field.
    pub fn mut_chunk_ids(&mut self) -> &mut ::std::vec::Vec<u64> {
        &mut self.chunk_ids
    }

    // Take field
    pub fn take_chunk_ids(&mut self) -> ::std::vec::Vec<u64> {
        ::std::mem::replace(&mut self.chunk_ids, ::std::vec::Vec::new())
    }

    // uint64 version = 3;


    pub fn get_version(&self) -> u64 {
        self.version
    }
    pub fn clear_version(&mut self) {
        self.version = 0;
    }

    // Param is passed by value, moved
    pub fn set_version(&mut self, v: u64) {
        self.version = v;
    }

    // bytes user_meta = 4;


    pub fn get_user_meta(&self) -> &[u8] {
        &self.user_meta
    }
    pub fn clear_user_meta(&mut self) {
        self.user_meta.clear();
    }

    // Param is passed by value, moved
    pub fn set_user_meta(&mut self, v: ::std::vec::Vec<u8>) {
        self.user_meta = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_user_meta(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.user_meta
    }

    // Take field
    pub fn take_user_meta(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.user_meta, ::std::vec::Vec::new())
    }

    // bytes lock_val_prefix = 5;


    pub fn get_lock_val_prefix(&self) -> &[u8] {
        &self.lock_val_prefix
    }
    pub fn clear_lock_val_prefix(&mut self) {
        self.lock_val_prefix.clear();
    }

    // Param is passed by value, moved
    pub fn set_lock_val_prefix(&mut self, v: ::std::vec::Vec<u8>) {
        self.lock_val_prefix = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_lock_val_prefix(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.lock_val_prefix
    }

    // Take field
    pub fn take_lock_val_prefix(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.lock_val_prefix, ::std::vec::Vec::new())
    }

    // uint64 shard_ver = 6;


    pub fn get_shard_ver(&self) -> u64 {
        self.shard_ver
    }
    pub fn clear_shard_ver(&mut self) {
        self.shard_ver = 0;
    }

    // Param is passed by value, moved
    pub fn set_shard_ver(&mut self, v: u64) {
        self.shard_ver = v;
    }

    // bytes inner_lower_bound = 7;


    pub fn get_inner_lower_bound(&self) -> &[u8] {
        &self.inner_lower_bound
    }
    pub fn clear_inner_lower_bound(&mut self) {
        self.inner_lower_bound.clear();
    }

    // Param is passed by value, moved
    pub fn set_inner_lower_bound(&mut self, v: ::std::vec::Vec<u8>) {
        self.inner_lower_bound = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_inner_lower_bound(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.inner_lower_bound
    }

    // Take field
    pub fn take_inner_lower_bound(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.inner_lower_bound, ::std::vec::Vec::new())
    }

    // bytes inner_upper_bound = 8;


    pub fn get_inner_upper_bound(&self) -> &[u8] {
        &self.inner_upper_bound
    }
    pub fn clear_inner_upper_bound(&mut self) {
        self.inner_upper_bound.clear();
    }

    // Param is passed by value, moved
    pub fn set_inner_upper_bound(&mut self, v: ::std::vec::Vec<u8>) {
        self.inner_upper_bound = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_inner_upper_bound(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.inner_upper_bound
    }

    // Take field
    pub fn take_inner_upper_bound(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.inner_upper_bound, ::std::vec::Vec::new())
    }
}

impl ::protobuf::Message for TxnFileRef {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.start_ts = tmp;
                },
                2 => {
                    ::protobuf::rt::read_repeated_uint64_into(wire_type, is, &mut self.chunk_ids)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.version = tmp;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.user_meta)?;
                },
                5 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.lock_val_prefix)?;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.shard_ver = tmp;
                },
                7 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.inner_lower_bound)?;
                },
                8 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.inner_upper_bound)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.start_ts != 0 {
            my_size += ::protobuf::rt::value_size(1, self.start_ts, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.chunk_ids {
            my_size += ::protobuf::rt::value_size(2, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        if self.version != 0 {
            my_size += ::protobuf::rt::value_size(3, self.version, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.user_meta.is_empty() {
            my_size += ::protobuf::rt::bytes_size(4, &self.user_meta);
        }
        if !self.lock_val_prefix.is_empty() {
            my_size += ::protobuf::rt::bytes_size(5, &self.lock_val_prefix);
        }
        if self.shard_ver != 0 {
            my_size += ::protobuf::rt::value_size(6, self.shard_ver, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.inner_lower_bound.is_empty() {
            my_size += ::protobuf::rt::bytes_size(7, &self.inner_lower_bound);
        }
        if !self.inner_upper_bound.is_empty() {
            my_size += ::protobuf::rt::bytes_size(8, &self.inner_upper_bound);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.start_ts != 0 {
            os.write_uint64(1, self.start_ts)?;
        }
        for v in &self.chunk_ids {
            os.write_uint64(2, *v)?;
        };
        if self.version != 0 {
            os.write_uint64(3, self.version)?;
        }
        if !self.user_meta.is_empty() {
            os.write_bytes(4, &self.user_meta)?;
        }
        if !self.lock_val_prefix.is_empty() {
            os.write_bytes(5, &self.lock_val_prefix)?;
        }
        if self.shard_ver != 0 {
            os.write_uint64(6, self.shard_ver)?;
        }
        if !self.inner_lower_bound.is_empty() {
            os.write_bytes(7, &self.inner_lower_bound)?;
        }
        if !self.inner_upper_bound.is_empty() {
            os.write_bytes(8, &self.inner_upper_bound)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> TxnFileRef {
        TxnFileRef::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "start_ts",
                    |m: &TxnFileRef| { &m.start_ts },
                    |m: &mut TxnFileRef| { &mut m.start_ts },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "chunk_ids",
                    |m: &TxnFileRef| { &m.chunk_ids },
                    |m: &mut TxnFileRef| { &mut m.chunk_ids },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "version",
                    |m: &TxnFileRef| { &m.version },
                    |m: &mut TxnFileRef| { &mut m.version },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "user_meta",
                    |m: &TxnFileRef| { &m.user_meta },
                    |m: &mut TxnFileRef| { &mut m.user_meta },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "lock_val_prefix",
                    |m: &TxnFileRef| { &m.lock_val_prefix },
                    |m: &mut TxnFileRef| { &mut m.lock_val_prefix },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "shard_ver",
                    |m: &TxnFileRef| { &m.shard_ver },
                    |m: &mut TxnFileRef| { &mut m.shard_ver },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "inner_lower_bound",
                    |m: &TxnFileRef| { &m.inner_lower_bound },
                    |m: &mut TxnFileRef| { &mut m.inner_lower_bound },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "inner_upper_bound",
                    |m: &TxnFileRef| { &m.inner_upper_bound },
                    |m: &mut TxnFileRef| { &mut m.inner_upper_bound },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<TxnFileRef>(
                    "TxnFileRef",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static TxnFileRef {
        static mut instance: ::protobuf::lazy::Lazy<TxnFileRef> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const TxnFileRef,
        };
        unsafe {
            instance.get(TxnFileRef::new)
        }
    }
}

impl ::protobuf::Clear for TxnFileRef {
    fn clear(&mut self) {
        self.start_ts = 0;
        self.chunk_ids.clear();
        self.version = 0;
        self.user_meta.clear();
        self.lock_val_prefix.clear();
        self.shard_ver = 0;
        self.inner_lower_bound.clear();
        self.inner_upper_bound.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for TxnFileRef {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.start_ts, "start_ts", buf);
        ::protobuf::PbPrint::fmt(&self.chunk_ids, "chunk_ids", buf);
        ::protobuf::PbPrint::fmt(&self.version, "version", buf);
        ::protobuf::PbPrint::fmt(&self.user_meta, "user_meta", buf);
        ::protobuf::PbPrint::fmt(&self.lock_val_prefix, "lock_val_prefix", buf);
        ::protobuf::PbPrint::fmt(&self.shard_ver, "shard_ver", buf);
        ::protobuf::PbPrint::fmt(&self.inner_lower_bound, "inner_lower_bound", buf);
        ::protobuf::PbPrint::fmt(&self.inner_upper_bound, "inner_upper_bound", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for TxnFileRef {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.start_ts, "start_ts", &mut s);
        ::protobuf::PbPrint::fmt(&self.chunk_ids, "chunk_ids", &mut s);
        ::protobuf::PbPrint::fmt(&self.version, "version", &mut s);
        ::protobuf::PbPrint::fmt(&self.user_meta, "user_meta", &mut s);
        ::protobuf::PbPrint::fmt(&self.lock_val_prefix, "lock_val_prefix", &mut s);
        ::protobuf::PbPrint::fmt(&self.shard_ver, "shard_ver", &mut s);
        ::protobuf::PbPrint::fmt(&self.inner_lower_bound, "inner_lower_bound", &mut s);
        ::protobuf::PbPrint::fmt(&self.inner_upper_bound, "inner_upper_bound", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for TxnFileRef {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct Schema {
    // message fields
    pub table_id: i64,
    pub columns: ::protobuf::RepeatedField<::std::vec::Vec<u8>>,
    pub pk_col_ids: ::std::vec::Vec<i64>,
    pub vector_indexes: ::protobuf::RepeatedField<VectorIndexDef>,
    pub fulltext_indexes: ::protobuf::RepeatedField<super::fts::FullTextIndexDef>,
    pub keys: ::protobuf::RepeatedField<::std::string::String>,
    pub values: ::protobuf::RepeatedField<::std::vec::Vec<u8>>,
    pub partitions: ::protobuf::RepeatedField<Partition>,
    pub max_col_id: i64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Schema {
    fn default() -> &'a Schema {
        <Schema as ::protobuf::Message>::default_instance()
    }
}

impl Schema {
    pub fn new() -> Schema {
        ::std::default::Default::default()
    }

    // int64 table_id = 1;


    pub fn get_table_id(&self) -> i64 {
        self.table_id
    }
    pub fn clear_table_id(&mut self) {
        self.table_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_table_id(&mut self, v: i64) {
        self.table_id = v;
    }

    // repeated bytes columns = 2;


    pub fn get_columns(&self) -> &[::std::vec::Vec<u8>] {
        &self.columns
    }
    pub fn clear_columns(&mut self) {
        self.columns.clear();
    }

    // Param is passed by value, moved
    pub fn set_columns(&mut self, v: ::protobuf::RepeatedField<::std::vec::Vec<u8>>) {
        self.columns = v;
    }

    // Mutable pointer to the field.
    pub fn mut_columns(&mut self) -> &mut ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        &mut self.columns
    }

    // Take field
    pub fn take_columns(&mut self) -> ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.columns, ::protobuf::RepeatedField::new())
    }

    // repeated int64 pk_col_ids = 3;


    pub fn get_pk_col_ids(&self) -> &[i64] {
        &self.pk_col_ids
    }
    pub fn clear_pk_col_ids(&mut self) {
        self.pk_col_ids.clear();
    }

    // Param is passed by value, moved
    pub fn set_pk_col_ids(&mut self, v: ::std::vec::Vec<i64>) {
        self.pk_col_ids = v;
    }

    // Mutable pointer to the field.
    pub fn mut_pk_col_ids(&mut self) -> &mut ::std::vec::Vec<i64> {
        &mut self.pk_col_ids
    }

    // Take field
    pub fn take_pk_col_ids(&mut self) -> ::std::vec::Vec<i64> {
        ::std::mem::replace(&mut self.pk_col_ids, ::std::vec::Vec::new())
    }

    // repeated .enginepb.VectorIndexDef vector_indexes = 4;


    pub fn get_vector_indexes(&self) -> &[VectorIndexDef] {
        &self.vector_indexes
    }
    pub fn clear_vector_indexes(&mut self) {
        self.vector_indexes.clear();
    }

    // Param is passed by value, moved
    pub fn set_vector_indexes(&mut self, v: ::protobuf::RepeatedField<VectorIndexDef>) {
        self.vector_indexes = v;
    }

    // Mutable pointer to the field.
    pub fn mut_vector_indexes(&mut self) -> &mut ::protobuf::RepeatedField<VectorIndexDef> {
        &mut self.vector_indexes
    }

    // Take field
    pub fn take_vector_indexes(&mut self) -> ::protobuf::RepeatedField<VectorIndexDef> {
        ::std::mem::replace(&mut self.vector_indexes, ::protobuf::RepeatedField::new())
    }

    // repeated .enginepb.FullTextIndexDef fulltext_indexes = 10;


    pub fn get_fulltext_indexes(&self) -> &[super::fts::FullTextIndexDef] {
        &self.fulltext_indexes
    }
    pub fn clear_fulltext_indexes(&mut self) {
        self.fulltext_indexes.clear();
    }

    // Param is passed by value, moved
    pub fn set_fulltext_indexes(&mut self, v: ::protobuf::RepeatedField<super::fts::FullTextIndexDef>) {
        self.fulltext_indexes = v;
    }

    // Mutable pointer to the field.
    pub fn mut_fulltext_indexes(&mut self) -> &mut ::protobuf::RepeatedField<super::fts::FullTextIndexDef> {
        &mut self.fulltext_indexes
    }

    // Take field
    pub fn take_fulltext_indexes(&mut self) -> ::protobuf::RepeatedField<super::fts::FullTextIndexDef> {
        ::std::mem::replace(&mut self.fulltext_indexes, ::protobuf::RepeatedField::new())
    }

    // repeated string keys = 5;


    pub fn get_keys(&self) -> &[::std::string::String] {
        &self.keys
    }
    pub fn clear_keys(&mut self) {
        self.keys.clear();
    }

    // Param is passed by value, moved
    pub fn set_keys(&mut self, v: ::protobuf::RepeatedField<::std::string::String>) {
        self.keys = v;
    }

    // Mutable pointer to the field.
    pub fn mut_keys(&mut self) -> &mut ::protobuf::RepeatedField<::std::string::String> {
        &mut self.keys
    }

    // Take field
    pub fn take_keys(&mut self) -> ::protobuf::RepeatedField<::std::string::String> {
        ::std::mem::replace(&mut self.keys, ::protobuf::RepeatedField::new())
    }

    // repeated bytes values = 6;


    pub fn get_values(&self) -> &[::std::vec::Vec<u8>] {
        &self.values
    }
    pub fn clear_values(&mut self) {
        self.values.clear();
    }

    // Param is passed by value, moved
    pub fn set_values(&mut self, v: ::protobuf::RepeatedField<::std::vec::Vec<u8>>) {
        self.values = v;
    }

    // Mutable pointer to the field.
    pub fn mut_values(&mut self) -> &mut ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        &mut self.values
    }

    // Take field
    pub fn take_values(&mut self) -> ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.values, ::protobuf::RepeatedField::new())
    }

    // repeated .enginepb.Partition partitions = 7;


    pub fn get_partitions(&self) -> &[Partition] {
        &self.partitions
    }
    pub fn clear_partitions(&mut self) {
        self.partitions.clear();
    }

    // Param is passed by value, moved
    pub fn set_partitions(&mut self, v: ::protobuf::RepeatedField<Partition>) {
        self.partitions = v;
    }

    // Mutable pointer to the field.
    pub fn mut_partitions(&mut self) -> &mut ::protobuf::RepeatedField<Partition> {
        &mut self.partitions
    }

    // Take field
    pub fn take_partitions(&mut self) -> ::protobuf::RepeatedField<Partition> {
        ::std::mem::replace(&mut self.partitions, ::protobuf::RepeatedField::new())
    }

    // int64 max_col_id = 8;


    pub fn get_max_col_id(&self) -> i64 {
        self.max_col_id
    }
    pub fn clear_max_col_id(&mut self) {
        self.max_col_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_max_col_id(&mut self, v: i64) {
        self.max_col_id = v;
    }
}

impl ::protobuf::Message for Schema {
    fn is_initialized(&self) -> bool {
        for v in &self.vector_indexes {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.fulltext_indexes {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.partitions {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.table_id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_repeated_bytes_into(wire_type, is, &mut self.columns)?;
                },
                3 => {
                    ::protobuf::rt::read_repeated_int64_into(wire_type, is, &mut self.pk_col_ids)?;
                },
                4 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.vector_indexes)?;
                },
                10 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.fulltext_indexes)?;
                },
                5 => {
                    ::protobuf::rt::read_repeated_string_into(wire_type, is, &mut self.keys)?;
                },
                6 => {
                    ::protobuf::rt::read_repeated_bytes_into(wire_type, is, &mut self.values)?;
                },
                7 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.partitions)?;
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.max_col_id = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.table_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.table_id, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.columns {
            my_size += ::protobuf::rt::bytes_size(2, &value);
        };
        for value in &self.pk_col_ids {
            my_size += ::protobuf::rt::value_size(3, *value, ::protobuf::wire_format::WireTypeVarint);
        };
        for value in &self.vector_indexes {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.fulltext_indexes {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        for value in &self.keys {
            my_size += ::protobuf::rt::string_size(5, &value);
        };
        for value in &self.values {
            my_size += ::protobuf::rt::bytes_size(6, &value);
        };
        for value in &self.partitions {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if self.max_col_id != 0 {
            my_size += ::protobuf::rt::value_size(8, self.max_col_id, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.table_id != 0 {
            os.write_int64(1, self.table_id)?;
        }
        for v in &self.columns {
            os.write_bytes(2, &v)?;
        };
        for v in &self.pk_col_ids {
            os.write_int64(3, *v)?;
        };
        for v in &self.vector_indexes {
            os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.fulltext_indexes {
            os.write_tag(10, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        for v in &self.keys {
            os.write_string(5, &v)?;
        };
        for v in &self.values {
            os.write_bytes(6, &v)?;
        };
        for v in &self.partitions {
            os.write_tag(7, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if self.max_col_id != 0 {
            os.write_int64(8, self.max_col_id)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Schema {
        Schema::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "table_id",
                    |m: &Schema| { &m.table_id },
                    |m: &mut Schema| { &mut m.table_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "columns",
                    |m: &Schema| { &m.columns },
                    |m: &mut Schema| { &mut m.columns },
                ));
                fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "pk_col_ids",
                    |m: &Schema| { &m.pk_col_ids },
                    |m: &mut Schema| { &mut m.pk_col_ids },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<VectorIndexDef>>(
                    "vector_indexes",
                    |m: &Schema| { &m.vector_indexes },
                    |m: &mut Schema| { &mut m.vector_indexes },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<super::fts::FullTextIndexDef>>(
                    "fulltext_indexes",
                    |m: &Schema| { &m.fulltext_indexes },
                    |m: &mut Schema| { &mut m.fulltext_indexes },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                    "keys",
                    |m: &Schema| { &m.keys },
                    |m: &mut Schema| { &mut m.keys },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "values",
                    |m: &Schema| { &m.values },
                    |m: &mut Schema| { &mut m.values },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<Partition>>(
                    "partitions",
                    |m: &Schema| { &m.partitions },
                    |m: &mut Schema| { &mut m.partitions },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "max_col_id",
                    |m: &Schema| { &m.max_col_id },
                    |m: &mut Schema| { &mut m.max_col_id },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<Schema>(
                    "Schema",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static Schema {
        static mut instance: ::protobuf::lazy::Lazy<Schema> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const Schema,
        };
        unsafe {
            instance.get(Schema::new)
        }
    }
}

impl ::protobuf::Clear for Schema {
    fn clear(&mut self) {
        self.table_id = 0;
        self.columns.clear();
        self.pk_col_ids.clear();
        self.vector_indexes.clear();
        self.fulltext_indexes.clear();
        self.keys.clear();
        self.values.clear();
        self.partitions.clear();
        self.max_col_id = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for Schema {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.table_id, "table_id", buf);
        ::protobuf::PbPrint::fmt(&self.columns, "columns", buf);
        ::protobuf::PbPrint::fmt(&self.pk_col_ids, "pk_col_ids", buf);
        ::protobuf::PbPrint::fmt(&self.vector_indexes, "vector_indexes", buf);
        ::protobuf::PbPrint::fmt(&self.fulltext_indexes, "fulltext_indexes", buf);
        ::protobuf::PbPrint::fmt(&self.keys, "keys", buf);
        ::protobuf::PbPrint::fmt(&self.values, "values", buf);
        ::protobuf::PbPrint::fmt(&self.partitions, "partitions", buf);
        ::protobuf::PbPrint::fmt(&self.max_col_id, "max_col_id", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for Schema {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.table_id, "table_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.columns, "columns", &mut s);
        ::protobuf::PbPrint::fmt(&self.pk_col_ids, "pk_col_ids", &mut s);
        ::protobuf::PbPrint::fmt(&self.vector_indexes, "vector_indexes", &mut s);
        ::protobuf::PbPrint::fmt(&self.fulltext_indexes, "fulltext_indexes", &mut s);
        ::protobuf::PbPrint::fmt(&self.keys, "keys", &mut s);
        ::protobuf::PbPrint::fmt(&self.values, "values", &mut s);
        ::protobuf::PbPrint::fmt(&self.partitions, "partitions", &mut s);
        ::protobuf::PbPrint::fmt(&self.max_col_id, "max_col_id", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for Schema {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct Partition {
    // message fields
    pub id: i64,
    pub keys: ::protobuf::RepeatedField<::std::string::String>,
    pub values: ::protobuf::RepeatedField<::std::vec::Vec<u8>>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a Partition {
    fn default() -> &'a Partition {
        <Partition as ::protobuf::Message>::default_instance()
    }
}

impl Partition {
    pub fn new() -> Partition {
        ::std::default::Default::default()
    }

    // int64 id = 1;


    pub fn get_id(&self) -> i64 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: i64) {
        self.id = v;
    }

    // repeated string keys = 2;


    pub fn get_keys(&self) -> &[::std::string::String] {
        &self.keys
    }
    pub fn clear_keys(&mut self) {
        self.keys.clear();
    }

    // Param is passed by value, moved
    pub fn set_keys(&mut self, v: ::protobuf::RepeatedField<::std::string::String>) {
        self.keys = v;
    }

    // Mutable pointer to the field.
    pub fn mut_keys(&mut self) -> &mut ::protobuf::RepeatedField<::std::string::String> {
        &mut self.keys
    }

    // Take field
    pub fn take_keys(&mut self) -> ::protobuf::RepeatedField<::std::string::String> {
        ::std::mem::replace(&mut self.keys, ::protobuf::RepeatedField::new())
    }

    // repeated bytes values = 3;


    pub fn get_values(&self) -> &[::std::vec::Vec<u8>] {
        &self.values
    }
    pub fn clear_values(&mut self) {
        self.values.clear();
    }

    // Param is passed by value, moved
    pub fn set_values(&mut self, v: ::protobuf::RepeatedField<::std::vec::Vec<u8>>) {
        self.values = v;
    }

    // Mutable pointer to the field.
    pub fn mut_values(&mut self) -> &mut ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        &mut self.values
    }

    // Take field
    pub fn take_values(&mut self) -> ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.values, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for Partition {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.id = tmp;
                },
                2 => {
                    ::protobuf::rt::read_repeated_string_into(wire_type, is, &mut self.keys)?;
                },
                3 => {
                    ::protobuf::rt::read_repeated_bytes_into(wire_type, is, &mut self.values)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.keys {
            my_size += ::protobuf::rt::string_size(2, &value);
        };
        for value in &self.values {
            my_size += ::protobuf::rt::bytes_size(3, &value);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_int64(1, self.id)?;
        }
        for v in &self.keys {
            os.write_string(2, &v)?;
        };
        for v in &self.values {
            os.write_bytes(3, &v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> Partition {
        Partition::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "id",
                    |m: &Partition| { &m.id },
                    |m: &mut Partition| { &mut m.id },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                    "keys",
                    |m: &Partition| { &m.keys },
                    |m: &mut Partition| { &mut m.keys },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "values",
                    |m: &Partition| { &m.values },
                    |m: &mut Partition| { &mut m.values },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<Partition>(
                    "Partition",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static Partition {
        static mut instance: ::protobuf::lazy::Lazy<Partition> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const Partition,
        };
        unsafe {
            instance.get(Partition::new)
        }
    }
}

impl ::protobuf::Clear for Partition {
    fn clear(&mut self) {
        self.id = 0;
        self.keys.clear();
        self.values.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for Partition {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.id, "id", buf);
        ::protobuf::PbPrint::fmt(&self.keys, "keys", buf);
        ::protobuf::PbPrint::fmt(&self.values, "values", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for Partition {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.id, "id", &mut s);
        ::protobuf::PbPrint::fmt(&self.keys, "keys", &mut s);
        ::protobuf::PbPrint::fmt(&self.values, "values", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for Partition {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct VectorIndex {
    // message fields
    pub table_id: i64,
    pub index_id: i64,
    pub col_id: i64,
    pub files: ::protobuf::RepeatedField<VectorIndexFile>,
    pub snap_version: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a VectorIndex {
    fn default() -> &'a VectorIndex {
        <VectorIndex as ::protobuf::Message>::default_instance()
    }
}

impl VectorIndex {
    pub fn new() -> VectorIndex {
        ::std::default::Default::default()
    }

    // int64 table_id = 1;


    pub fn get_table_id(&self) -> i64 {
        self.table_id
    }
    pub fn clear_table_id(&mut self) {
        self.table_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_table_id(&mut self, v: i64) {
        self.table_id = v;
    }

    // int64 index_id = 2;


    pub fn get_index_id(&self) -> i64 {
        self.index_id
    }
    pub fn clear_index_id(&mut self) {
        self.index_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_index_id(&mut self, v: i64) {
        self.index_id = v;
    }

    // int64 col_id = 3;


    pub fn get_col_id(&self) -> i64 {
        self.col_id
    }
    pub fn clear_col_id(&mut self) {
        self.col_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_col_id(&mut self, v: i64) {
        self.col_id = v;
    }

    // repeated .enginepb.VectorIndexFile files = 4;


    pub fn get_files(&self) -> &[VectorIndexFile] {
        &self.files
    }
    pub fn clear_files(&mut self) {
        self.files.clear();
    }

    // Param is passed by value, moved
    pub fn set_files(&mut self, v: ::protobuf::RepeatedField<VectorIndexFile>) {
        self.files = v;
    }

    // Mutable pointer to the field.
    pub fn mut_files(&mut self) -> &mut ::protobuf::RepeatedField<VectorIndexFile> {
        &mut self.files
    }

    // Take field
    pub fn take_files(&mut self) -> ::protobuf::RepeatedField<VectorIndexFile> {
        ::std::mem::replace(&mut self.files, ::protobuf::RepeatedField::new())
    }

    // uint64 snap_version = 5;


    pub fn get_snap_version(&self) -> u64 {
        self.snap_version
    }
    pub fn clear_snap_version(&mut self) {
        self.snap_version = 0;
    }

    // Param is passed by value, moved
    pub fn set_snap_version(&mut self, v: u64) {
        self.snap_version = v;
    }
}

impl ::protobuf::Message for VectorIndex {
    fn is_initialized(&self) -> bool {
        for v in &self.files {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.table_id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.index_id = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.col_id = tmp;
                },
                4 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.files)?;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.snap_version = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.table_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.table_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.index_id != 0 {
            my_size += ::protobuf::rt::value_size(2, self.index_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.col_id != 0 {
            my_size += ::protobuf::rt::value_size(3, self.col_id, ::protobuf::wire_format::WireTypeVarint);
        }
        for value in &self.files {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if self.snap_version != 0 {
            my_size += ::protobuf::rt::value_size(5, self.snap_version, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.table_id != 0 {
            os.write_int64(1, self.table_id)?;
        }
        if self.index_id != 0 {
            os.write_int64(2, self.index_id)?;
        }
        if self.col_id != 0 {
            os.write_int64(3, self.col_id)?;
        }
        for v in &self.files {
            os.write_tag(4, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if self.snap_version != 0 {
            os.write_uint64(5, self.snap_version)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> VectorIndex {
        VectorIndex::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "table_id",
                    |m: &VectorIndex| { &m.table_id },
                    |m: &mut VectorIndex| { &mut m.table_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "index_id",
                    |m: &VectorIndex| { &m.index_id },
                    |m: &mut VectorIndex| { &mut m.index_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "col_id",
                    |m: &VectorIndex| { &m.col_id },
                    |m: &mut VectorIndex| { &mut m.col_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<VectorIndexFile>>(
                    "files",
                    |m: &VectorIndex| { &m.files },
                    |m: &mut VectorIndex| { &mut m.files },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "snap_version",
                    |m: &VectorIndex| { &m.snap_version },
                    |m: &mut VectorIndex| { &mut m.snap_version },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<VectorIndex>(
                    "VectorIndex",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static VectorIndex {
        static mut instance: ::protobuf::lazy::Lazy<VectorIndex> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const VectorIndex,
        };
        unsafe {
            instance.get(VectorIndex::new)
        }
    }
}

impl ::protobuf::Clear for VectorIndex {
    fn clear(&mut self) {
        self.table_id = 0;
        self.index_id = 0;
        self.col_id = 0;
        self.files.clear();
        self.snap_version = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for VectorIndex {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.table_id, "table_id", buf);
        ::protobuf::PbPrint::fmt(&self.index_id, "index_id", buf);
        ::protobuf::PbPrint::fmt(&self.col_id, "col_id", buf);
        ::protobuf::PbPrint::fmt(&self.files, "files", buf);
        ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for VectorIndex {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.table_id, "table_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.index_id, "index_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.col_id, "col_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.files, "files", &mut s);
        ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for VectorIndex {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct VectorIndexFile {
    // message fields
    pub id: u64,
    pub snap_version: u64,
    pub smallest: ::std::vec::Vec<u8>,
    pub biggest: ::std::vec::Vec<u8>,
    pub meta_offset: u32,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a VectorIndexFile {
    fn default() -> &'a VectorIndexFile {
        <VectorIndexFile as ::protobuf::Message>::default_instance()
    }
}

impl VectorIndexFile {
    pub fn new() -> VectorIndexFile {
        ::std::default::Default::default()
    }

    // uint64 id = 1;


    pub fn get_id(&self) -> u64 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: u64) {
        self.id = v;
    }

    // uint64 snap_version = 2;


    pub fn get_snap_version(&self) -> u64 {
        self.snap_version
    }
    pub fn clear_snap_version(&mut self) {
        self.snap_version = 0;
    }

    // Param is passed by value, moved
    pub fn set_snap_version(&mut self, v: u64) {
        self.snap_version = v;
    }

    // bytes smallest = 3;


    pub fn get_smallest(&self) -> &[u8] {
        &self.smallest
    }
    pub fn clear_smallest(&mut self) {
        self.smallest.clear();
    }

    // Param is passed by value, moved
    pub fn set_smallest(&mut self, v: ::std::vec::Vec<u8>) {
        self.smallest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_smallest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.smallest
    }

    // Take field
    pub fn take_smallest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.smallest, ::std::vec::Vec::new())
    }

    // bytes biggest = 4;


    pub fn get_biggest(&self) -> &[u8] {
        &self.biggest
    }
    pub fn clear_biggest(&mut self) {
        self.biggest.clear();
    }

    // Param is passed by value, moved
    pub fn set_biggest(&mut self, v: ::std::vec::Vec<u8>) {
        self.biggest = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_biggest(&mut self) -> &mut ::std::vec::Vec<u8> {
        &mut self.biggest
    }

    // Take field
    pub fn take_biggest(&mut self) -> ::std::vec::Vec<u8> {
        ::std::mem::replace(&mut self.biggest, ::std::vec::Vec::new())
    }

    // uint32 meta_offset = 6;


    pub fn get_meta_offset(&self) -> u32 {
        self.meta_offset
    }
    pub fn clear_meta_offset(&mut self) {
        self.meta_offset = 0;
    }

    // Param is passed by value, moved
    pub fn set_meta_offset(&mut self, v: u32) {
        self.meta_offset = v;
    }
}

impl ::protobuf::Message for VectorIndexFile {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.snap_version = tmp;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.smallest)?;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_bytes_into(wire_type, is, &mut self.biggest)?;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint32()?;
                    self.meta_offset = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.snap_version != 0 {
            my_size += ::protobuf::rt::value_size(2, self.snap_version, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.smallest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(3, &self.smallest);
        }
        if !self.biggest.is_empty() {
            my_size += ::protobuf::rt::bytes_size(4, &self.biggest);
        }
        if self.meta_offset != 0 {
            my_size += ::protobuf::rt::value_size(6, self.meta_offset, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_uint64(1, self.id)?;
        }
        if self.snap_version != 0 {
            os.write_uint64(2, self.snap_version)?;
        }
        if !self.smallest.is_empty() {
            os.write_bytes(3, &self.smallest)?;
        }
        if !self.biggest.is_empty() {
            os.write_bytes(4, &self.biggest)?;
        }
        if self.meta_offset != 0 {
            os.write_uint32(6, self.meta_offset)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> VectorIndexFile {
        VectorIndexFile::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "id",
                    |m: &VectorIndexFile| { &m.id },
                    |m: &mut VectorIndexFile| { &mut m.id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "snap_version",
                    |m: &VectorIndexFile| { &m.snap_version },
                    |m: &mut VectorIndexFile| { &mut m.snap_version },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "smallest",
                    |m: &VectorIndexFile| { &m.smallest },
                    |m: &mut VectorIndexFile| { &mut m.smallest },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "biggest",
                    |m: &VectorIndexFile| { &m.biggest },
                    |m: &mut VectorIndexFile| { &mut m.biggest },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint32>(
                    "meta_offset",
                    |m: &VectorIndexFile| { &m.meta_offset },
                    |m: &mut VectorIndexFile| { &mut m.meta_offset },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<VectorIndexFile>(
                    "VectorIndexFile",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static VectorIndexFile {
        static mut instance: ::protobuf::lazy::Lazy<VectorIndexFile> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const VectorIndexFile,
        };
        unsafe {
            instance.get(VectorIndexFile::new)
        }
    }
}

impl ::protobuf::Clear for VectorIndexFile {
    fn clear(&mut self) {
        self.id = 0;
        self.snap_version = 0;
        self.smallest.clear();
        self.biggest.clear();
        self.meta_offset = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for VectorIndexFile {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.id, "id", buf);
        ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", buf);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", buf);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", buf);
        ::protobuf::PbPrint::fmt(&self.meta_offset, "meta_offset", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for VectorIndexFile {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.id, "id", &mut s);
        ::protobuf::PbPrint::fmt(&self.snap_version, "snap_version", &mut s);
        ::protobuf::PbPrint::fmt(&self.smallest, "smallest", &mut s);
        ::protobuf::PbPrint::fmt(&self.biggest, "biggest", &mut s);
        ::protobuf::PbPrint::fmt(&self.meta_offset, "meta_offset", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for VectorIndexFile {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct VectorIndexDef {
    // message fields
    pub index_id: i64,
    pub col_id: i64,
    pub index_kind: ::std::string::String,
    pub spec_keys: ::protobuf::RepeatedField<::std::string::String>,
    pub spec_values: ::protobuf::RepeatedField<::std::vec::Vec<u8>>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a VectorIndexDef {
    fn default() -> &'a VectorIndexDef {
        <VectorIndexDef as ::protobuf::Message>::default_instance()
    }
}

impl VectorIndexDef {
    pub fn new() -> VectorIndexDef {
        ::std::default::Default::default()
    }

    // int64 index_id = 1;


    pub fn get_index_id(&self) -> i64 {
        self.index_id
    }
    pub fn clear_index_id(&mut self) {
        self.index_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_index_id(&mut self, v: i64) {
        self.index_id = v;
    }

    // int64 col_id = 2;


    pub fn get_col_id(&self) -> i64 {
        self.col_id
    }
    pub fn clear_col_id(&mut self) {
        self.col_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_col_id(&mut self, v: i64) {
        self.col_id = v;
    }

    // string index_kind = 3;


    pub fn get_index_kind(&self) -> &str {
        &self.index_kind
    }
    pub fn clear_index_kind(&mut self) {
        self.index_kind.clear();
    }

    // Param is passed by value, moved
    pub fn set_index_kind(&mut self, v: ::std::string::String) {
        self.index_kind = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_index_kind(&mut self) -> &mut ::std::string::String {
        &mut self.index_kind
    }

    // Take field
    pub fn take_index_kind(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.index_kind, ::std::string::String::new())
    }

    // repeated string spec_keys = 4;


    pub fn get_spec_keys(&self) -> &[::std::string::String] {
        &self.spec_keys
    }
    pub fn clear_spec_keys(&mut self) {
        self.spec_keys.clear();
    }

    // Param is passed by value, moved
    pub fn set_spec_keys(&mut self, v: ::protobuf::RepeatedField<::std::string::String>) {
        self.spec_keys = v;
    }

    // Mutable pointer to the field.
    pub fn mut_spec_keys(&mut self) -> &mut ::protobuf::RepeatedField<::std::string::String> {
        &mut self.spec_keys
    }

    // Take field
    pub fn take_spec_keys(&mut self) -> ::protobuf::RepeatedField<::std::string::String> {
        ::std::mem::replace(&mut self.spec_keys, ::protobuf::RepeatedField::new())
    }

    // repeated bytes spec_values = 5;


    pub fn get_spec_values(&self) -> &[::std::vec::Vec<u8>] {
        &self.spec_values
    }
    pub fn clear_spec_values(&mut self) {
        self.spec_values.clear();
    }

    // Param is passed by value, moved
    pub fn set_spec_values(&mut self, v: ::protobuf::RepeatedField<::std::vec::Vec<u8>>) {
        self.spec_values = v;
    }

    // Mutable pointer to the field.
    pub fn mut_spec_values(&mut self) -> &mut ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        &mut self.spec_values
    }

    // Take field
    pub fn take_spec_values(&mut self) -> ::protobuf::RepeatedField<::std::vec::Vec<u8>> {
        ::std::mem::replace(&mut self.spec_values, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for VectorIndexDef {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.index_id = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_int64()?;
                    self.col_id = tmp;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.index_kind)?;
                },
                4 => {
                    ::protobuf::rt::read_repeated_string_into(wire_type, is, &mut self.spec_keys)?;
                },
                5 => {
                    ::protobuf::rt::read_repeated_bytes_into(wire_type, is, &mut self.spec_values)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.index_id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.index_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.col_id != 0 {
            my_size += ::protobuf::rt::value_size(2, self.col_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if !self.index_kind.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.index_kind);
        }
        for value in &self.spec_keys {
            my_size += ::protobuf::rt::string_size(4, &value);
        };
        for value in &self.spec_values {
            my_size += ::protobuf::rt::bytes_size(5, &value);
        };
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.index_id != 0 {
            os.write_int64(1, self.index_id)?;
        }
        if self.col_id != 0 {
            os.write_int64(2, self.col_id)?;
        }
        if !self.index_kind.is_empty() {
            os.write_string(3, &self.index_kind)?;
        }
        for v in &self.spec_keys {
            os.write_string(4, &v)?;
        };
        for v in &self.spec_values {
            os.write_bytes(5, &v)?;
        };
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> VectorIndexDef {
        VectorIndexDef::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "index_id",
                    |m: &VectorIndexDef| { &m.index_id },
                    |m: &mut VectorIndexDef| { &mut m.index_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeInt64>(
                    "col_id",
                    |m: &VectorIndexDef| { &m.col_id },
                    |m: &mut VectorIndexDef| { &mut m.col_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                    "index_kind",
                    |m: &VectorIndexDef| { &m.index_kind },
                    |m: &mut VectorIndexDef| { &mut m.index_kind },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                    "spec_keys",
                    |m: &VectorIndexDef| { &m.spec_keys },
                    |m: &mut VectorIndexDef| { &mut m.spec_keys },
                ));
                fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeBytes>(
                    "spec_values",
                    |m: &VectorIndexDef| { &m.spec_values },
                    |m: &mut VectorIndexDef| { &mut m.spec_values },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<VectorIndexDef>(
                    "VectorIndexDef",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static VectorIndexDef {
        static mut instance: ::protobuf::lazy::Lazy<VectorIndexDef> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const VectorIndexDef,
        };
        unsafe {
            instance.get(VectorIndexDef::new)
        }
    }
}

impl ::protobuf::Clear for VectorIndexDef {
    fn clear(&mut self) {
        self.index_id = 0;
        self.col_id = 0;
        self.index_kind.clear();
        self.spec_keys.clear();
        self.spec_values.clear();
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for VectorIndexDef {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.index_id, "index_id", buf);
        ::protobuf::PbPrint::fmt(&self.col_id, "col_id", buf);
        ::protobuf::PbPrint::fmt(&self.index_kind, "index_kind", buf);
        ::protobuf::PbPrint::fmt(&self.spec_keys, "spec_keys", buf);
        ::protobuf::PbPrint::fmt(&self.spec_values, "spec_values", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for VectorIndexDef {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.index_id, "index_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.col_id, "col_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.index_kind, "index_kind", &mut s);
        ::protobuf::PbPrint::fmt(&self.spec_keys, "spec_keys", &mut s);
        ::protobuf::PbPrint::fmt(&self.spec_values, "spec_values", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for VectorIndexDef {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct FtsL0Create {
    // message fields
    pub id: u64,
    pub source_l0_sst_id: u64,
    pub version: u64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a FtsL0Create {
    fn default() -> &'a FtsL0Create {
        <FtsL0Create as ::protobuf::Message>::default_instance()
    }
}

impl FtsL0Create {
    pub fn new() -> FtsL0Create {
        ::std::default::Default::default()
    }

    // uint64 id = 1;


    pub fn get_id(&self) -> u64 {
        self.id
    }
    pub fn clear_id(&mut self) {
        self.id = 0;
    }

    // Param is passed by value, moved
    pub fn set_id(&mut self, v: u64) {
        self.id = v;
    }

    // uint64 source_l0_sst_id = 4;


    pub fn get_source_l0_sst_id(&self) -> u64 {
        self.source_l0_sst_id
    }
    pub fn clear_source_l0_sst_id(&mut self) {
        self.source_l0_sst_id = 0;
    }

    // Param is passed by value, moved
    pub fn set_source_l0_sst_id(&mut self, v: u64) {
        self.source_l0_sst_id = v;
    }

    // uint64 version = 5;


    pub fn get_version(&self) -> u64 {
        self.version
    }
    pub fn clear_version(&mut self) {
        self.version = 0;
    }

    // Param is passed by value, moved
    pub fn set_version(&mut self, v: u64) {
        self.version = v;
    }
}

impl ::protobuf::Message for FtsL0Create {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.id = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.source_l0_sst_id = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_uint64()?;
                    self.version = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.id != 0 {
            my_size += ::protobuf::rt::value_size(1, self.id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.source_l0_sst_id != 0 {
            my_size += ::protobuf::rt::value_size(4, self.source_l0_sst_id, ::protobuf::wire_format::WireTypeVarint);
        }
        if self.version != 0 {
            my_size += ::protobuf::rt::value_size(5, self.version, ::protobuf::wire_format::WireTypeVarint);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream) -> ::protobuf::ProtobufResult<()> {
        if self.id != 0 {
            os.write_uint64(1, self.id)?;
        }
        if self.source_l0_sst_id != 0 {
            os.write_uint64(4, self.source_l0_sst_id)?;
        }
        if self.version != 0 {
            os.write_uint64(5, self.version)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> FtsL0Create {
        FtsL0Create::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static mut descriptor: ::protobuf::lazy::Lazy<::protobuf::reflect::MessageDescriptor> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const ::protobuf::reflect::MessageDescriptor,
        };
        unsafe {
            descriptor.get(|| {
                let mut fields = ::std::vec::Vec::new();
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "id",
                    |m: &FtsL0Create| { &m.id },
                    |m: &mut FtsL0Create| { &mut m.id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "source_l0_sst_id",
                    |m: &FtsL0Create| { &m.source_l0_sst_id },
                    |m: &mut FtsL0Create| { &mut m.source_l0_sst_id },
                ));
                fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeUint64>(
                    "version",
                    |m: &FtsL0Create| { &m.version },
                    |m: &mut FtsL0Create| { &mut m.version },
                ));
                ::protobuf::reflect::MessageDescriptor::new::<FtsL0Create>(
                    "FtsL0Create",
                    fields,
                    file_descriptor_proto()
                )
            })
        }
    }

    fn default_instance() -> &'static FtsL0Create {
        static mut instance: ::protobuf::lazy::Lazy<FtsL0Create> = ::protobuf::lazy::Lazy {
            lock: ::protobuf::lazy::ONCE_INIT,
            ptr: 0 as *const FtsL0Create,
        };
        unsafe {
            instance.get(FtsL0Create::new)
        }
    }
}

impl ::protobuf::Clear for FtsL0Create {
    fn clear(&mut self) {
        self.id = 0;
        self.source_l0_sst_id = 0;
        self.version = 0;
        self.unknown_fields.clear();
    }
}

impl ::protobuf::PbPrint for FtsL0Create {
    #[allow(unused_variables)]
    fn fmt(&self, name: &str, buf: &mut String) {
        ::protobuf::push_message_start(name, buf);
        let old_len = buf.len();
        ::protobuf::PbPrint::fmt(&self.id, "id", buf);
        ::protobuf::PbPrint::fmt(&self.source_l0_sst_id, "source_l0_sst_id", buf);
        ::protobuf::PbPrint::fmt(&self.version, "version", buf);
        if old_len < buf.len() {
          buf.push(' ');
        }
        buf.push('}');
    }
}
impl ::std::fmt::Debug for FtsL0Create {
    #[allow(unused_variables)]
    fn fmt(&self, f: &mut ::std::fmt::Formatter) -> ::std::fmt::Result {
        let mut s = String::new();
        ::protobuf::PbPrint::fmt(&self.id, "id", &mut s);
        ::protobuf::PbPrint::fmt(&self.source_l0_sst_id, "source_l0_sst_id", &mut s);
        ::protobuf::PbPrint::fmt(&self.version, "version", &mut s);
        write!(f, "{}", s)
    }
}

impl ::protobuf::reflect::ProtobufValue for FtsL0Create {
    fn as_ref(&self) -> ::protobuf::reflect::ProtobufValueRef {
        ::protobuf::reflect::ProtobufValueRef::Message(self)
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x0fchangeset.proto\x12\x08enginepb\"\xde\x07\n\tChangeSet\x12\x11\n\
    \x07shardID\x18\x01\x20\x01(\x04B\0\x12\x12\n\x08shardVer\x18\x02\x20\
    \x01(\x04B\0\x12*\n\ncompaction\x18\x04\x20\x01(\x0b2\x14.enginepb.Compa\
    ctionB\0\x12\x20\n\x05flush\x18\x05\x20\x01(\x0b2\x0f.enginepb.FlushB\0\
    \x12&\n\x08snapshot\x18\x06\x20\x01(\x0b2\x12.enginepb.SnapshotB\0\x12+\
    \n\rinitial_flush\x18\x07\x20\x01(\x0b2\x12.enginepb.SnapshotB\0\x12\x20\
    \n\x05split\x18\n\x20\x01(\x0b2\x0f.enginepb.SplitB\0\x12\x15\n\x0bshard\
    Delete\x18\x0b\x20\x01(\x08B\0\x12\x12\n\x08sequence\x18\x0c\x20\x01(\
    \x04B\0\x12%\n\x06parent\x18\r\x20\x01(\x0b2\x13.enginepb.ChangeSetB\0\
    \x12-\n\x0cingest_files\x18\x0e\x20\x01(\x0b2\x15.enginepb.IngestFilesB\
    \0\x12\x16\n\x0cproperty_key\x18\x0f\x20\x01(\tB\0\x12\x18\n\x0eproperty\
    _value\x18\x10\x20\x01(\x0cB\0\x12\x18\n\x0eproperty_merge\x18\x11\x20\
    \x01(\x08B\0\x12.\n\rdestroy_range\x18\x12\x20\x01(\x0b2\x15.enginepb.Ta\
    bleChangeB\0\x12,\n\x0btruncate_ts\x18\x13\x20\x01(\x0b2\x15.enginepb.Ta\
    bleChangeB\0\x120\n\x0ftrim_over_bound\x18\x14\x20\x01(\x0b2\x15.enginep\
    b.TableChangeB\0\x12+\n\rrestore_shard\x18\x15\x20\x01(\x0b2\x12.enginep\
    b.SnapshotB\0\x125\n\x10major_compaction\x18\x16\x20\x01(\x0b2\x19.engin\
    epb.MajorCompactionB\0\x122\n\x12update_schema_meta\x18\x17\x20\x01(\x0b\
    2\x14.enginepb.SchemaMetaB\0\x12;\n\x13columnar_compaction\x18\x18\x20\
    \x01(\x0b2\x1c.enginepb.ColumnarCompactionB\0\x12:\n\x13update_vector_in\
    dex\x18\x19\x20\x01(\x0b2\x1b.enginepb.UpdateVectorIndexB\0\x12\x18\n\
    \x0eclear_columnar\x18\x1a\x20\x01(\x08B\0\x12+\n\rsnapshot_diff\x18\x1b\
    \x20\x01(\x0b2\x12.enginepb.SnapshotB\0\x12.\n\rfts_l0_create\x18\x1c\
    \x20\x01(\x0b2\x15.enginepb.FtsL0CreateB\0:\0\":\n\nChangeSets\x12*\n\
    \x0bchange_sets\x18\x01\x20\x03(\x0b2\x13.enginepb.ChangeSetB\0:\0\"\xfe\
    \x01\n\nCompaction\x12\x0c\n\x02cf\x18\x01\x20\x01(\x05B\0\x12\x0f\n\x05\
    level\x18\x02\x20\x01(\rB\0\x12-\n\x0ctableCreates\x18\x03\x20\x03(\x0b2\
    \x15.enginepb.TableCreateB\0\x12\x14\n\ntopDeletes\x18\x04\x20\x03(\x04B\
    \0\x12\x17\n\rbottomDeletes\x18\x05\x20\x03(\x04B\0\x12\x14\n\nconflicte\
    d\x18\x06\x20\x01(\x08B\0\x12*\n\nblobTables\x18\x07\x20\x03(\x0b2\x14.e\
    nginepb.BlobCreateB\0\x12/\n\x0efts_l0_creates\x18\x08\x20\x03(\x0b2\x15\
    .enginepb.FtsL0CreateB\0:\0\"\xc4\x01\n\x0fMajorCompaction\x12.\n\rsstab\
    leChange\x18\x01\x20\x01(\x0b2\x15.enginepb.TableChangeB\0\x12-\n\rnewBl\
    obTables\x18\x02\x20\x03(\x0b2\x14.enginepb.BlobCreateB\0\x12\x17\n\rold\
    BlobTables\x18\x03\x20\x03(\x04B\0\x12\x14\n\nconflicted\x18\x04\x20\x01\
    (\x08B\0\x12!\n\x17update_inner_key_offset\x18\x05\x20\x01(\x08B\0:\0\"f\
    \n\nSchemaMeta\x12\x15\n\x0bkeyspace_id\x18\x01\x20\x01(\rB\0\x12\x11\n\
    \x07file_id\x18\x02\x20\x01(\x04B\0\x12\x11\n\x07version\x18\x03\x20\x01\
    (\x03B\0\x12\x19\n\x0frestore_version\x18\x04\x20\x01(\x04B\0:\0\"\xd0\
    \x01\n\x12ColumnarCompaction\x120\n\x0fcolumnar_change\x18\x01\x20\x01(\
    \x0b2\x15.enginepb.TableChangeB\0\x12\x16\n\x0csnap_version\x18\x02\x20\
    \x01(\x04B\0\x12\x11\n\x07row_l0s\x18\x03\x20\x03(\x04B\0\x12\x16\n\x0ct\
    arget_level\x18\x04\x20\x01(\rB\0\x12\x1c\n\x12columnar_table_ids\x18\
    \x05\x20\x03(\x03B\0\x12%\n\x1bcolumnar_table_ids_to_clear\x18\x06\x20\
    \x03(\x03B\0:\0\"\xa6\x01\n\x11UpdateVectorIndex\x12\x12\n\x08table_id\
    \x18\x01\x20\x01(\x03B\0\x12\x12\n\x08index_id\x18\x02\x20\x01(\x03B\0\
    \x12\x10\n\x06col_id\x18\x03\x20\x01(\x03B\0\x12*\n\x05added\x18\x04\x20\
    \x03(\x0b2\x19.enginepb.VectorIndexFileB\0\x12\x11\n\x07removed\x18\x05\
    \x20\x03(\x04B\0\x12\x16\n\x0csnap_version\x18\x06\x20\x01(\x04B\0:\0\"\
    \xab\x01\n\x05Flush\x12&\n\x08l0Create\x18\x01\x20\x01(\x0b2\x12.enginep\
    b.L0CreateB\0\x12*\n\nproperties\x18\x02\x20\x01(\x0b2\x14.enginepb.Prop\
    ertiesB\0\x12\x11\n\x07version\x18\x03\x20\x01(\x04B\0\x12\x10\n\x06max_\
    ts\x18\x05\x20\x01(\x04B\0\x12'\n\tl0Creates\x18\x06\x20\x03(\x0b2\x12.e\
    nginepb.L0CreateB\0:\0\"\x85\x05\n\x08Snapshot\x12\x15\n\x0bouter_start\
    \x18\x01\x20\x01(\x0cB\0\x12\x13\n\touter_end\x18\x02\x20\x01(\x0cB\0\
    \x12*\n\nproperties\x18\x03\x20\x01(\x0b2\x14.enginepb.PropertiesB\0\x12\
    '\n\tl0Creates\x18\x05\x20\x03(\x0b2\x12.enginepb.L0CreateB\0\x12-\n\x0c\
    tableCreates\x18\x06\x20\x03(\x0b2\x15.enginepb.TableCreateB\0\x12\x15\n\
    \x0bbaseVersion\x18\x07\x20\x01(\x04B\0\x12\x17\n\rdata_sequence\x18\x08\
    \x20\x01(\x04B\0\x12+\n\x0bBlobCreates\x18\t\x20\x03(\x0b2\x14.enginepb.\
    BlobCreateB\0\x12\x10\n\x06max_ts\x18\n\x20\x01(\x04B\0\x12\x17\n\rinner\
    _key_off\x18\x0b\x20\x01(\rB\0\x123\n\x0fcolumnarCreates\x18\x0c\x20\x03\
    (\x0b2\x18.enginepb.ColumnarCreateB\0\x12+\n\x0bschema_meta\x18\r\x20\
    \x01(\x0b2\x14.enginepb.SchemaMetaB\0\x12\x19\n\x0funconverted_l0s\x18\
    \x0f\x20\x03(\x04B\0\x12/\n\x0evector_indexes\x18\x10\x20\x03(\x0b2\x15.\
    enginepb.VectorIndexB\0\x12\x1c\n\x12columnar_table_ids\x18\x11\x20\x03(\
    \x03B\0\x12\"\n\x18columnar_l2_snap_version\x18\x12\x20\x01(\x04B\0\x120\
    \n\x0ffts_l0_segments\x18\x1e\x20\x03(\x0b2\x15.enginepb.FtsL0CreateB\0\
    \x12\x1d\n\x13fts_unconverted_l0s\x18\x1f\x20\x03(\x04B\0:\0\"Q\n\x08L0C\
    reate\x12\x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\x12\x12\n\x08smallest\x18\
    \x02\x20\x01(\x0cB\0\x12\x11\n\x07biggest\x18\x03\x20\x01(\x0cB\0\x12\
    \x0e\n\x04size\x18\x04\x20\x01(\rB\0:\0\"C\n\nBlobCreate\x12\x0c\n\x02ID\
    \x18\x01\x20\x01(\x04B\0\x12\x12\n\x08smallest\x18\x02\x20\x01(\x0cB\0\
    \x12\x11\n\x07biggest\x18\x03\x20\x01(\x0cB\0:\0\"z\n\x0bTableCreate\x12\
    \x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\x12\x0f\n\x05level\x18\x02\x20\x01(\
    \rB\0\x12\x0c\n\x02CF\x18\x03\x20\x01(\x05B\0\x12\x12\n\x08smallest\x18\
    \x04\x20\x01(\x0cB\0\x12\x11\n\x07biggest\x18\x05\x20\x01(\x0cB\0\x12\
    \x15\n\x0bmeta_offset\x18\x07\x20\x01(\rB\0:\0\"<\n\x0bTableDelete\x12\
    \x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\x12\x0f\n\x05level\x18\x02\x20\x01(\
    \rB\0\x12\x0c\n\x02CF\x18\x03\x20\x01(\x05B\0:\0\"o\n\x0eColumnarCreate\
    \x12\x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\x12\x0f\n\x05level\x18\x02\x20\
    \x01(\rB\0\x12\x12\n\x08smallest\x18\x03\x20\x01(\x0cB\0\x12\x11\n\x07bi\
    ggest\x18\x04\x20\x01(\x0cB\0\x12\x15\n\x0bmeta_offset\x18\x05\x20\x01(\
    \rB\0:\0\"1\n\x0eColumnarDelete\x12\x0c\n\x02ID\x18\x01\x20\x01(\x04B\0\
    \x12\x0f\n\x05level\x18\x02\x20\x01(\rB\0:\0\"D\n\x05Split\x12)\n\tnewSh\
    ards\x18\x01\x20\x03(\x0b2\x14.enginepb.PropertiesB\0\x12\x0e\n\x04Keys\
    \x18\x03\x20\x03(\x0cB\0:\0\"\xd2\x01\n\x0bIngestFiles\x12'\n\tl0Creates\
    \x18\x01\x20\x03(\x0b2\x12.enginepb.L0CreateB\0\x12-\n\x0ctableCreates\
    \x18\x02\x20\x03(\x0b2\x15.enginepb.TableCreateB\0\x12*\n\nproperties\
    \x18\x03\x20\x01(\x0b2\x14.enginepb.PropertiesB\0\x12+\n\x0bBlobCreates\
    \x18\x04\x20\x03(\x0b2\x14.enginepb.BlobCreateB\0\x12\x10\n\x06max_ts\
    \x18\x05\x20\x01(\x04B\0:\0\"C\n\nProperties\x12\x11\n\x07shardID\x18\
    \x01\x20\x01(\x04B\0\x12\x0e\n\x04keys\x18\x02\x20\x03(\tB\0\x12\x10\n\
    \x06values\x18\x03\x20\x03(\x0cB\0:\0\"\xef\x01\n\x0bTableChange\x12-\n\
    \x0ctableDeletes\x18\x01\x20\x03(\x0b2\x15.enginepb.TableDeleteB\0\x12-\
    \n\x0ctableCreates\x18\x02\x20\x03(\x0b2\x15.enginepb.TableCreateB\0\x12\
    \x16\n\x0cfile_ids_map\x18\x03\x20\x03(\x04B\0\x123\n\x0fcolumnarDeletes\
    \x18\x04\x20\x03(\x0b2\x18.enginepb.ColumnarDeleteB\0\x123\n\x0fcolumnar\
    Creates\x18\x05\x20\x03(\x0b2\x18.enginepb.ColumnarCreateB\0:\0\">\n\x0b\
    TxnFileRefs\x12-\n\rtxn_file_refs\x18\x01\x20\x03(\x0b2\x14.enginepb.Txn\
    FileRefB\0:\0\"\xc9\x01\n\nTxnFileRef\x12\x12\n\x08start_ts\x18\x01\x20\
    \x01(\x04B\0\x12\x13\n\tchunk_ids\x18\x02\x20\x03(\x04B\0\x12\x11\n\x07v\
    ersion\x18\x03\x20\x01(\x04B\0\x12\x13\n\tuser_meta\x18\x04\x20\x01(\x0c\
    B\0\x12\x19\n\x0flock_val_prefix\x18\x05\x20\x01(\x0cB\0\x12\x13\n\tshar\
    d_ver\x18\x06\x20\x01(\x04B\0\x12\x1b\n\x11inner_lower_bound\x18\x07\x20\
    \x01(\x0cB\0\x12\x1b\n\x11inner_upper_bound\x18\x08\x20\x01(\x0cB\0:\0\"\
    \x96\x02\n\x06Schema\x12\x12\n\x08table_id\x18\x01\x20\x01(\x03B\0\x12\
    \x11\n\x07columns\x18\x02\x20\x03(\x0cB\0\x12\x14\n\npk_col_ids\x18\x03\
    \x20\x03(\x03B\0\x122\n\x0evector_indexes\x18\x04\x20\x03(\x0b2\x18.engi\
    nepb.VectorIndexDefB\0\x126\n\x10fulltext_indexes\x18\n\x20\x03(\x0b2\
    \x1a.enginepb.FullTextIndexDefB\0\x12\x0e\n\x04keys\x18\x05\x20\x03(\tB\
    \0\x12\x10\n\x06values\x18\x06\x20\x03(\x0cB\0\x12)\n\npartitions\x18\
    \x07\x20\x03(\x0b2\x13.enginepb.PartitionB\0\x12\x14\n\nmax_col_id\x18\
    \x08\x20\x01(\x03B\0:\0\"=\n\tPartition\x12\x0c\n\x02id\x18\x01\x20\x01(\
    \x03B\0\x12\x0e\n\x04keys\x18\x02\x20\x03(\tB\0\x12\x10\n\x06values\x18\
    \x03\x20\x03(\x0cB\0:\0\"\x8d\x01\n\x0bVectorIndex\x12\x12\n\x08table_id\
    \x18\x01\x20\x01(\x03B\0\x12\x12\n\x08index_id\x18\x02\x20\x01(\x03B\0\
    \x12\x10\n\x06col_id\x18\x03\x20\x01(\x03B\0\x12*\n\x05files\x18\x04\x20\
    \x03(\x0b2\x19.enginepb.VectorIndexFileB\0\x12\x16\n\x0csnap_version\x18\
    \x05\x20\x01(\x04B\0:\0\"w\n\x0fVectorIndexFile\x12\x0c\n\x02id\x18\x01\
    \x20\x01(\x04B\0\x12\x16\n\x0csnap_version\x18\x02\x20\x01(\x04B\0\x12\
    \x12\n\x08smallest\x18\x03\x20\x01(\x0cB\0\x12\x11\n\x07biggest\x18\x04\
    \x20\x01(\x0cB\0\x12\x15\n\x0bmeta_offset\x18\x06\x20\x01(\rB\0:\0\"z\n\
    \x0eVectorIndexDef\x12\x12\n\x08index_id\x18\x01\x20\x01(\x03B\0\x12\x10\
    \n\x06col_id\x18\x02\x20\x01(\x03B\0\x12\x14\n\nindex_kind\x18\x03\x20\
    \x01(\tB\0\x12\x13\n\tspec_keys\x18\x04\x20\x03(\tB\0\x12\x15\n\x0bspec_\
    values\x18\x05\x20\x03(\x0cB\0:\0\"L\n\x0bFtsL0Create\x12\x0c\n\x02id\
    \x18\x01\x20\x01(\x04B\0\x12\x1a\n\x10source_l0_sst_id\x18\x04\x20\x01(\
    \x04B\0\x12\x11\n\x07version\x18\x05\x20\x01(\x04B\0:\0B\0b\x06proto3\
";

static mut file_descriptor_proto_lazy: ::protobuf::lazy::Lazy<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::lazy::Lazy {
    lock: ::protobuf::lazy::ONCE_INIT,
    ptr: 0 as *const ::protobuf::descriptor::FileDescriptorProto,
};

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    unsafe {
        file_descriptor_proto_lazy.get(|| {
            parse_descriptor_proto()
        })
    }
}
