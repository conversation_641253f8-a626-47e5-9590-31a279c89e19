// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::{
    collections::{HashMap, VecDeque},
    fmt::Debug,
    sync::{Arc, OnceLock, RwLock},
};

use anyhow::{anyhow, bail, Result};
use bytes::{Buf, Bytes};
use clara_fts::{self, IndexReader as ClaraIndexReader};
use hexhex::hex;
use kvenginepb::fts as ftspb;
use protobuf::Message;
use tantivy::Index;
use tipb::FtsQueryInfo;
use xorf::{BinaryFuse8, Filter};

use crate::{
    codecutil::BytesExt,
    ia::ia_file::IaFile,
    table::{
        file::{File, TtlCache},
        ChecksumType,
    },
};

mod builder;
mod mmap_directory;
#[cfg(test)]
mod test;

pub use builder::*;
pub use mmap_directory::*;

/// Magic number for FTS DedicatedFile format
pub const FTS_DEDICATED_FILE_MAGIC: u32 = 0xFEE74B2F;

/// Format version for FTS DedicatedFile
pub const FTS_DEDICATED_FILE_FORMAT_V1: u8 = 0x1;

/// Size of serialized footer in bytes
pub const FTS_DEDICATED_FILE_FOOTER_SIZE: usize = 52;

// TODO List:
// 1. Modify the parameters of the search function to avoid word segmentation
//    for each query
// 2. Currently, DedicatedFile mmaps the tantivy index data in .xxx files, which
//    ultimately results in the entire index data being downloaded. This
//    requires more refined handling of on-demand download requirements.
// 3. Add integrity check for DedicatedFile's data block.

/// Footer has a fixed size and is always located at the end of the file.
/// The footer size cannot be changed.
/// Format:
/// - 1 byte: format version
/// - 1 byte: checksum type
/// - 6 bytes: reserved
/// - 4 bytes: checksum of footer
/// - 4 bytes: checksum of remaining meta blocks
/// - 8 bytes: data block offset (Tantivy data)
/// - 8 bytes: pk filter block offset
/// - 8 bytes: handle index block offset
/// - 8 bytes: property block offset
/// - 4 bytes: magic number
#[repr(C)]
#[derive(Default, Clone, Copy, Debug)]
pub struct DedicatedFileFooter {
    pub format: u8,
    pub checksum_type: ChecksumType,
    pub checksum_other_meta: u32,
    pub data_block_offset: u64,
    pub pk_filter_block_offset: u64,
    pub handle_index_block_offset: u64,
    pub prop_offset: u64,
    pub magic: u32,
}

impl DedicatedFileFooter {
    /// Get the size of the footer
    pub const fn footer_size() -> usize {
        FTS_DEDICATED_FILE_FOOTER_SIZE
    }

    /// Create a new footer with default values
    pub fn new() -> Self {
        Self {
            format: FTS_DEDICATED_FILE_FORMAT_V1,
            checksum_type: ChecksumType::None,
            checksum_other_meta: 0,
            data_block_offset: 0,
            pk_filter_block_offset: 0,
            handle_index_block_offset: 0,
            prop_offset: 0,
            magic: FTS_DEDICATED_FILE_MAGIC,
        }
    }

    /// Marshal footer to bytes
    pub fn marshal<W: std::io::Write>(&self, mut w: W) -> Result<usize> {
        use bytes::BufMut;

        let mut footer = [0u8; FTS_DEDICATED_FILE_FOOTER_SIZE];
        let mut data = &mut footer[..];

        // Write fields according to the format specification
        data.put_u8(self.format); // 1 byte: format version
        data.put_u8(self.checksum_type.value()); // 1 byte: checksum type
        data.put_slice(&[0; 6]); // 6 bytes: reserved
        data.put_slice(&[0; 4]); // 4 bytes: placeholder for checksum of footer
        data.put_u32_le(self.checksum_other_meta); // 4 bytes: checksum of remaining meta blocks
        data.put_u64_le(self.data_block_offset); // 8 bytes: data block offset (Tantivy data)
        data.put_u64_le(self.pk_filter_block_offset); // 8 bytes: pk filter block offset
        data.put_u64_le(self.handle_index_block_offset); // 8 bytes: handle index block offset
        data.put_u64_le(self.prop_offset); // 8 bytes: property block offset
        data.put_u32_le(self.magic); // 4 bytes: magic number

        // Calculate and write the footer checksum
        let checksum_footer = self.checksum_type.checksum(&footer);
        footer[8..12].copy_from_slice(&checksum_footer.to_le_bytes());

        w.write_all(&footer)?;

        Ok(FTS_DEDICATED_FILE_FOOTER_SIZE)
    }

    /// Unmarshal footer from bytes
    pub fn unmarshal(mut data: &[u8]) -> Result<Self> {
        if data.len() != FTS_DEDICATED_FILE_FOOTER_SIZE {
            bail!(
                "Invalid footer size, expected {:#x}, got {:#x}",
                FTS_DEDICATED_FILE_FOOTER_SIZE,
                data.len()
            );
        }
        let mut copied_data = [0u8; FTS_DEDICATED_FILE_FOOTER_SIZE];
        copied_data.copy_from_slice(data);

        // Read fields according to the format specification
        let format = data.get_u8(); // 1 byte: format version
        let checksum_type_value = data.get_u8(); // 1 byte: checksum type
        let checksum_type = ChecksumType::from(checksum_type_value);
        data.advance(6); // 6 bytes: reserved
        let checksum_footer = data.get_u32_le(); // 4 bytes: checksum of footer
        let checksum_other_meta = data.get_u32_le(); // 4 bytes: checksum of remaining meta blocks
        let data_block_offset = data.get_u64_le(); // 8 bytes: data block offset (Tantivy data)
        let pk_filter_block_offset = data.get_u64_le(); // 8 bytes: pk filter block offset
        let handle_index_block_offset = data.get_u64_le(); // 8 bytes: handle index block offset
        let prop_offset = data.get_u64_le(); // 8 bytes: property block offset
        let magic = data.get_u32_le(); // 4 bytes: magic number

        // Validate magic number
        if magic != FTS_DEDICATED_FILE_MAGIC {
            bail!(
                "Invalid magic number, expected {:#x}, got {:#x}",
                FTS_DEDICATED_FILE_MAGIC,
                magic
            );
        }

        // Validate format version
        if format != FTS_DEDICATED_FILE_FORMAT_V1 {
            bail!(
                "Unsupported format version, expected {:#x}, got {:#x}",
                FTS_DEDICATED_FILE_FORMAT_V1,
                format
            );
        }

        {
            // Verify footer's checksum
            copied_data[8..12].fill(0);
            let checksum_footer_actual = checksum_type.checksum(&copied_data);
            if checksum_footer != checksum_footer_actual {
                copied_data[8..12].copy_from_slice(&checksum_footer.to_le_bytes());
                bail!(
                    "FtsDedicatedFile footer checksum mismatch, actual {:#08x}, expect {:#08x}, footer data: {}, ChecksumType={:?}",
                    checksum_footer_actual,
                    checksum_footer,
                    hex(&copied_data),
                    checksum_type,
                );
            }
        }

        Ok(Self {
            format,
            checksum_type,
            checksum_other_meta,
            data_block_offset,
            pk_filter_block_offset,
            handle_index_block_offset,
            prop_offset,
            magic,
        })
    }
}

/// An in-memory FtsDedicatedFile. Only minimal metadata is kept in memory.
/// Main data is accessed through mmap, accelerated data is cached through
/// TtlCache.
#[derive(Clone)]
pub struct DedicatedFile<Info>(Arc<DedicatedFileCore<Info>>);

/// Core implementation of DedicatedFile
struct DedicatedFileCore<Info> {
    file: Arc<IaFile>,

    handle_index_block: TtlCache<ftspb::DedicatedFileHandleIndexBlock>,
    pk_filter: TtlCache<BinaryFuse8>,
    props: ftspb::DedicatedFilePropertyBlock,
    footer: DedicatedFileFooter,
    handle_block_checked: RwLock<Vec<bool>>, // AtomicBool would be better

    handle_block_cache: TtlCache<HandleBlockAccessor>,

    // Tantivy index cache - Caches initialized IndexReaders.
    tantivy_index_cache: OnceLock<ClaraIndexReader>,

    additional_info: Info,
}

impl DedicatedFile<()> {
    /// Create a new PackedFile from a remote file.
    pub fn new(file: Arc<IaFile>) -> Result<DedicatedFile<()>> {
        DedicatedFile::<()>::new_with_info(file, ())
    }

    /// Calculate IA segment offsets for an FTS dedicated file based on handle
    /// blocks and data block. This function reads the handle index block and
    /// calculates segment boundaries based on the standard IA segment size.
    ///
    /// DedicatedFile layout: Handle Blocks -> Data Block -> Metadata Blocks ->
    /// Footer We need to create segments that respect both handle block
    /// boundaries and the large data block.
    pub fn generate_ia_segment_boundaries(file: &IaFile, segment_size: u64) -> Result<Vec<u64>> {
        let footer = Self::load_footer(file)?;
        let handle_index_block = Self::load_handle_index_block(file, &footer)?;

        let mut segment_offsets = vec![0]; // Always start with offset 0
        let mut current_segment_start = 0u64;

        // 1. First, create segments based on handle block boundaries
        // Handle blocks are typically small and numerous, so we group them into
        // segments
        for &block_offset in &handle_index_block.handle_block_offsets {
            // If this handle block would make the current segment exceed the segment size,
            // start a new segment
            if block_offset >= current_segment_start + segment_size {
                segment_offsets.push(block_offset);
                current_segment_start = block_offset;
            }
        }

        // 2. Handle the data block (Tantivy data) which is typically very large
        // The data block starts after all handle blocks
        let data_block_start = footer.data_block_offset;
        if data_block_start >= current_segment_start + segment_size {
            segment_offsets.push(data_block_start);
            current_segment_start = data_block_start;
        }

        // 3. Create segment boundaries based on individual Tantivy files within the
        //    data_block
        // Load props to get Tantivy file information
        let props = Self::load_props(file, &footer)?;
        let tantivy_files = props.get_tantivy_files();

        // Collect boundaries of all Tantivy files
        let mut tantivy_file_boundaries = Vec::new();
        let file_types = [
            ("meta.json", tantivy_files.get_meta()),
            (".managed.json", tantivy_files.get_managed()),
            (".term", tantivy_files.get_term()),
            (".idx", tantivy_files.get_idx()),
            (".pos", tantivy_files.get_pos()),
            (".store", tantivy_files.get_store()),
            (".fast", tantivy_files.get_fast()),
            (".fieldnorm", tantivy_files.get_fieldnorm()),
        ];

        // Calculate absolute offset and end position for each Tantivy file
        for (name, file_range) in file_types {
            if file_range.get_size() > 0 {
                let file_start = data_block_start + file_range.get_offset();
                let file_end = file_start + file_range.get_size();
                tantivy_file_boundaries.push((file_start, file_end, name));
            }
        }

        // Sort by file start position
        tantivy_file_boundaries.sort_by_key(|&(start, ..)| start);

        // Create segment boundaries for each Tantivy file
        for &(file_start, _file_end, _name) in &tantivy_file_boundaries {
            // If the file start is far enough from the current segment start, create a new
            // segment
            if file_start > current_segment_start
                && (file_start >= current_segment_start + segment_size
                    || segment_offsets.last() != Some(&file_start))
            {
                segment_offsets.push(file_start);
                current_segment_start = file_start;
            }
        }

        segment_offsets.push(footer.pk_filter_block_offset);

        Ok(segment_offsets)
    }

    /// Load the footer from the packed file.
    fn load_footer(file: &IaFile) -> Result<DedicatedFileFooter> {
        let footer_data = file.read_footer(FTS_DEDICATED_FILE_FOOTER_SIZE)?;
        let footer = DedicatedFileFooter::unmarshal(footer_data.as_ref())?;
        Self::verify_meta_checksum(file, &footer)?;
        Ok(footer)
    }

    /// Load the properties block from the dedicated file.
    /// Property block is the last metadata block, located between prop_offset
    /// and footer. According to DedicatedFileBuilder write order: Handle
    /// Index -> PK Filter -> Property -> Footer
    fn load_props(
        file: &IaFile,
        footer: &DedicatedFileFooter,
    ) -> Result<ftspb::DedicatedFilePropertyBlock> {
        let prop_offset = footer.prop_offset;
        let file_size = file.size();
        let footer_start = file_size - FTS_DEDICATED_FILE_FOOTER_SIZE as u64;

        // Verify that Property Block offset is valid
        if prop_offset >= footer_start {
            bail!(
                "Invalid property block offset {:#x}, footer starts at {:#x}",
                prop_offset,
                footer_start
            );
        }

        // Property Block is located between prop_offset and footer start
        let prop_len = (footer_start - prop_offset) as usize;
        if prop_len == 0 {
            bail!("Property block has zero size, this should not happen");
        }

        let data = file.read_table_meta(prop_offset, prop_len)?;
        let mut prop = ftspb::DedicatedFilePropertyBlock::new();
        prop.merge_from_bytes(&data)
            .map_err(|e| anyhow!("Failed to parse property block: {}", e))?;

        // Verify that required fields are present
        if prop.get_lp_key().is_empty() {
            bail!("Property block is missing lp_key field");
        }

        Ok(prop)
    }

    /// Verify the metadata checksum for the dedicated file.
    /// The checksum covers metadata blocks in order: handle index, pk filter,
    /// and property blocks. This matches the order used in
    /// DedicatedFileBuilder::finish().
    fn verify_meta_checksum(file: &IaFile, footer: &DedicatedFileFooter) -> Result<()> {
        let mut meta_checksum = 0u32;

        let file_size = file.size();
        let footer_start = file_size - FTS_DEDICATED_FILE_FOOTER_SIZE as u64;
        let meta_start = footer.handle_index_block_offset;
        let meta_len = footer_start - meta_start;
        let meta_data = file.read_table_meta(meta_start, meta_len as usize)?;
        meta_checksum = footer.checksum_type.append(meta_checksum, &meta_data);

        // Verify accumulated checksum
        if meta_checksum != footer.checksum_other_meta {
            bail!(
                "FtsDedicatedFile meta checksum mismatch, actual {:#08x}, expect {:#08x}, ChecksumType={:?}",
                meta_checksum,
                footer.checksum_other_meta,
                footer.checksum_type
            );
        }
        Ok(())
    }

    /// Load the handle index block from the dedicated file.
    fn load_handle_index_block(
        file: &IaFile,
        footer: &DedicatedFileFooter,
    ) -> Result<ftspb::DedicatedFileHandleIndexBlock> {
        let offset = footer.handle_index_block_offset;
        let length = footer.pk_filter_block_offset - footer.handle_index_block_offset;
        let data = file.read_table_meta(offset, length as usize)?;

        let mut handle_index_block = ftspb::DedicatedFileHandleIndexBlock::new();
        handle_index_block.merge_from_bytes(&data)?;

        if handle_index_block.handle_block_start_key.is_empty()
            || handle_index_block.handle_block_offsets.is_empty()
        {
            bail!(
                "handle_index_block broken: handle_block_start_key and handle_block_offsets must not be empty"
            );
        }
        if handle_index_block.handle_block_start_key.len() + 1
            != handle_index_block.handle_block_offsets.len()
        {
            bail!(
                "handle_index_block broken: handle_block_start_key.len={} but handle_block_offsets.len={}, expected N+1 relationship",
                handle_index_block.handle_block_start_key.len(),
                handle_index_block.handle_block_offsets.len()
            );
        }

        Ok(handle_index_block)
    }

    /// Load the PK filter block from the dedicated file.
    fn load_pk_filter(file: &IaFile, footer: &DedicatedFileFooter) -> Result<BinaryFuse8> {
        let offset = footer.pk_filter_block_offset;
        let length = footer.prop_offset - footer.pk_filter_block_offset;
        let data = file.read_table_meta(offset, length as usize)?;

        let filter =
            BinaryFuse8::try_from_bytes(&data).map_err(|e| anyhow!("Bad pk filter: {}", e))?;
        Ok(filter)
    }

    // used for testing
    #[cfg(test)]
    fn load_handle_block_by_idx(
        file: &IaFile,
        footer: &DedicatedFileFooter,
        idx: usize,
    ) -> Result<HandleBlockAccessor> {
        // 1. First load handle index block to get offset information
        let handle_index_block = Self::load_handle_index_block(file, footer)?;

        // 2. Verify that index is valid (should be less than number of handle blocks)
        if idx >= handle_index_block.handle_block_start_key.len() {
            bail!(
                "Handle block index {} out of bounds, max index is {}",
                idx,
                handle_index_block.handle_block_start_key.len() - 1
            );
        }

        // 3. Calculate offset and size of the idx-th handle block
        let block_start_offset = handle_index_block.handle_block_offsets[idx];

        // For DedicatedFile, handle_block_offsets has N+1 elements for N handle blocks
        // This allows us to calculate block size as: offsets[i+1] - offsets[i]
        let block_end_offset = handle_index_block.handle_block_offsets[idx + 1];

        let block_size = (block_end_offset - block_start_offset) as usize;

        // 4. Read raw data of handle block
        // Note: Since HandleBlockAccessor needs aligned data, we need to ensure data is
        // properly aligned
        let block_data = file.read(block_start_offset, block_size)?;

        // If data is not properly aligned, throw an error
        let aligned_data = if block_data.as_ptr() as usize % 8 != 0 {
            bail!("Handle block data is not 8-byte aligned, cannot load into HandleBlockAccessor");
        } else {
            block_data
        };

        // 5. Load properties to determine if it's integer PK
        let props = Self::load_props(file, footer)?;
        let is_int_handle = props.get_is_int_handle();

        // 6. Create HandleBlockAccessor and verify checksum if needed
        let block_accessor = HandleBlockAccessor::new(aligned_data, is_int_handle)?;

        // Note: We don't implement checksum verification here because this is a static
        // method and doesn't have access to handle_block_checked. Checksum
        // verification should be done by the caller if needed.

        Ok(block_accessor)
    }
}

impl<Info: Debug> Debug for DedicatedFile<Info> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("DedicateddFile")
            .field("file", &self.0.file)
            .field("props", &self.0.props)
            .field("info", &self.0.additional_info)
            .finish()
    }
}

impl<Info> DedicatedFile<Info> {
    // ... new, load_footer, load_props, load_pk_filter and other methods ...
    // [These method implementations are very similar to PackedFile, just with
    // different offsets and metadata structures]

    pub fn new_with_info(file: Arc<IaFile>, additional_info: Info) -> Result<DedicatedFile<Info>> {
        if file.is_sync() {
            // Currently local file is not supported.
            bail!(
                "FtsDedicatedFile can be only accessed via IaFile, got {:?}",
                file.path()
            );
        }

        let footer = DedicatedFile::load_footer(file.as_ref())?;
        let props = DedicatedFile::load_props(file.as_ref(), &footer)?;
        Ok(Self(Arc::new(DedicatedFileCore::<_> {
            file,
            handle_index_block: TtlCache::default(),
            pk_filter: TtlCache::default(),
            props,
            footer,
            handle_block_checked: RwLock::new(vec![]),
            handle_block_cache: TtlCache::default(),

            // Initialize Tantivy index cache.
            tantivy_index_cache: OnceLock::new(),

            additional_info,
        })))
    }

    /// Get or load Handle index block.
    pub fn get_handle_index_block(&self) -> Result<Arc<ftspb::DedicatedFileHandleIndexBlock>> {
        // ... TtlCache get-or-create implementation ...
        self.0
            .handle_index_block
            .get(|| DedicatedFile::load_handle_index_block(&self.0.file, &self.0.footer))
    }

    /// Get or load primary key filter.
    pub fn get_pk_filter(&self) -> Result<Arc<BinaryFuse8>> {
        // ... TtlCache get-or-create implementation ...
        self.0
            .pk_filter
            .get(|| DedicatedFile::load_pk_filter(&self.0.file, &self.0.footer))
    }

    /// Get footer.
    pub fn get_footer(&self) -> &DedicatedFileFooter {
        &self.0.footer
    }

    /// Return property block.
    pub fn get_props(&self) -> &ftspb::DedicatedFilePropertyBlock {
        &self.0.props
    }

    /// Full-text search function, using mmap and caching for efficient Tantivy
    /// index queries.
    ///
    /// Search process:
    /// 1. Try to get IndexReader from cache.
    /// 2. If cache misses, mmap the Data Block, create a custom Directory, and
    ///    initialize the IndexReader.
    /// 3. Use the IndexReader to execute the query and return results.
    ///
    /// # Arguments
    /// * `fts_query` - Full-text search query information.
    ///
    /// # Returns
    /// * `Ok((results, documents))` - Search results and corresponding document
    ///   contents.
    /// * `Err(error)` - An error during the search process.
    pub async fn search(&self, fts_query: &FtsQueryInfo) -> Result<Vec<clara_fts::ScoredResult>> {
        // Step 1: Try to get IndexReader from cache.
        let index_reader = match self.0.tantivy_index_cache.get() {
            Some(reader) => reader,
            None => {
                let reader = self.create_index_reader().await?;
                // Try to set the value, but if another thread already set it, use that one
                match self.0.tantivy_index_cache.set(reader) {
                    Ok(()) => self.0.tantivy_index_cache.get().unwrap(),
                    Err(_reader) => {
                        // Another thread already set the value, use the existing one
                        // The reader we created is returned in the Err, but we don't need it
                        self.0.tantivy_index_cache.get().unwrap()
                    }
                }
            }
        };

        // Step 2: Execute the query using the IndexReader.
        let mut results = Vec::new();
        let filter = clara_fts::BitmapFilter::all_match();

        match fts_query.get_query_type() {
            tipb::FtsQueryType::FtsQueryTypeNoScore => {
                let mut doc_ids = Vec::new();
                index_reader.search_no_score(fts_query.get_query_text(), &filter, &mut doc_ids)?;

                // Convert to ScoredResult format (with a score of 0).
                for doc_id in doc_ids {
                    results.push(clara_fts::ScoredResult { doc_id, score: 0.0 });
                }
            }
            tipb::FtsQueryType::FtsQueryTypeWithScore => {
                index_reader.search_scored(fts_query.get_query_text(), &filter, &mut results)?;
            }
            _ => {
                bail!(
                    "Unsupported FTS query type: {:?}",
                    fts_query.get_query_type()
                );
            }
        }

        Ok(results)
    }

    /// Creates an IndexReader - this method is called on a cache miss.
    ///
    /// Implementation steps:
    /// 1. Mmap the entire Data Block.
    /// 2. Create the custom MmapDirectory.
    /// 3. Initialize the Tantivy Index.
    /// 4. Create a clara_fts::IndexReader.
    async fn create_index_reader(&self) -> Result<ClaraIndexReader> {
        // Step 1: Mmap the entire Data Block.
        let footer = &self.0.footer;
        let props = &self.0.props;

        // Calculate the size of the Data Block.
        // The Data Block starts at data_block_offset and ends at the first metadata
        // block (handle_index_block).
        let data_block_start = footer.data_block_offset;

        // Use chunked reading to avoid cross-segment read issues.
        let mut file_chunks: std::collections::HashMap<String, Bytes> =
            std::collections::HashMap::new();

        // Get Tantivy file info and read each file individually.
        let tantivy_files = props.get_tantivy_files();
        let file_types = [
            ("meta.json", tantivy_files.get_meta()),
            (".managed.json", tantivy_files.get_managed()),
            (".term", tantivy_files.get_term()),
            (".idx", tantivy_files.get_idx()),
            (".pos", tantivy_files.get_pos()),
            (".store", tantivy_files.get_store()),
            (".fast", tantivy_files.get_fast()),
            (".fieldnorm", tantivy_files.get_fieldnorm()),
        ];

        // Collect read information for all files and sort by offset.
        for (name, file_range) in file_types {
            if file_range.get_size() > 0 {
                let file_offset = data_block_start + file_range.get_offset();
                let file_size = file_range.get_size();

                let (chunk, _) = self
                    .0
                    .file
                    .mmap_range(file_offset, file_size as usize)
                    .await?;

                file_chunks.insert(name.to_string(), chunk);
            }
        }

        let directory = MmapDirectory::new(file_chunks);

        // Step 3: Initialize the Tantivy Index.
        let mut index = Index::open(directory)?;

        // Set the tokenizer (using the one provided by clara_fts).
        index.set_tokenizers(clara_fts::TOKENIZERS.clone());

        // Step 4: Create a clara_fts::IndexReader.
        let index_reader = ClaraIndexReader::from_tantivy_index(index)?;

        Ok(index_reader)
    }

    /// Check if this file stores the specified integer PK with version >
    /// given_version and <= max_version. This is used for MVCC checks.
    pub async fn has_newer_version_int(&self, pk: i64, version: u64, max_version: u64) -> Result<bool> {
        // 1. First check file-level PK filter
        let pk_bytes = pk.to_be_bytes();
        let pk_filter = self.get_pk_filter()?;
        let pk_hash = farmhash::fingerprint64(pk_bytes.as_slice());
        if !pk_filter.contains(&pk_hash) {
            return Ok(false);
        }

        // 2. Find Handle Block that might contain this PK in Handle index block
        let handle_index = self.get_handle_index_block()?;
        let block_idx = match self.find_int_handle_block_index(pk)? {
            Some(idx) => idx,
            None => return Ok(false),
        };

        // 3. Get Handle Block data and verify
        let block_accessor = self.get_handle_block_accessor(block_idx, &handle_index, true).await?;

        block_accessor.has_newer_version_int(pk, version, max_version)
    }

    /// Check if this file stores the specified common PK with version >
    /// given_version and <= max_version. This is used for MVCC checks.
    pub async fn has_newer_version_common(
        &self,
        pk: &[u8],
        version: u64,
        max_version: u64,
    ) -> Result<bool> {
        // 1. First check file-level PK filter
        let pk_filter = self.get_pk_filter()?;
        let pk_hash = farmhash::fingerprint64(pk);
        if !pk_filter.contains(&pk_hash) {
            return Ok(false);
        }

        // 2. Find Handle Block that might contain this PK in Handle index block
        let handle_index = self.get_handle_index_block()?;
        let block_idx = match self.find_common_handle_block_index(pk)? {
            Some(idx) => idx,
            None => return Ok(false),
        };

        // 3. Get Handle Block data and verify
        let block_accessor = self.get_handle_block_accessor(block_idx, &handle_index, false).await?;

        block_accessor.has_newer_version_common(pk, version, max_version)
    }

    /// Helper method to get a handle block accessor with checksum verification
    async fn get_handle_block_accessor(
        &self,
        block_idx: usize,
        handle_index: &ftspb::DedicatedFileHandleIndexBlock,
        is_int_handle: bool,
    ) -> Result<Arc<HandleBlockAccessor>> {
        // Try to get from cache first
        let cache 

        // Cache miss, load from disk
        let block_accessor =
            self.load_handle_block_from_disk(block_idx, handle_index, is_int_handle).await?;
        let arc_accessor = Arc::new(block_accessor);

        // Cache the accessor for future use
        {
            let mut cache = self.0.handle_block_cache.write().unwrap();
            cache.insert(block_idx, arc_accessor.clone());
        }

        Ok(arc_accessor)
    }

    /// Load handle block from disk with checksum verification
    async fn load_handle_block_from_disk(
        &self,
        block_idx: usize,
        handle_index: &ftspb::DedicatedFileHandleIndexBlock,
        is_int_handle: bool,
    ) -> Result<HandleBlockAccessor> {
        // Get Handle Block data
        if block_idx + 1 >= handle_index.handle_block_offsets.len() {
            bail!("Handle block index {} out of bounds", block_idx);
        }
        let block_offset = handle_index.handle_block_offsets[block_idx];

        // Calculate block end offset using N+1 offset array
        let block_end_offset = handle_index.handle_block_offsets[block_idx + 1];
        let block_len = block_end_offset - block_offset;

        let (data, _) = self.0.file.mmap_range(block_offset, block_len as usize).await?;

        // Use HandleBlockAccessor to search within the block
        let block_accessor = HandleBlockAccessor::new(data, is_int_handle)?;

        // Verify checksum if not already verified
        let need_checksum = {
            let handle_block_checked = self.0.handle_block_checked.read().unwrap();
            handle_block_checked.is_empty()
                || !handle_block_checked.get(block_idx).unwrap_or(&false)
        };

        if need_checksum {
            let checksum_type = ChecksumType::from(self.0.footer.checksum_type as u8);
            block_accessor.verify_checksum(checksum_type)?;

            // Mark this handle block as verified
            {
                let mut handle_block_checked = self.0.handle_block_checked.write().unwrap();
                if handle_block_checked.is_empty() {
                    handle_block_checked.resize(handle_index.handle_block_offsets.len(), false);
                }
                if block_idx < handle_block_checked.len() {
                    handle_block_checked[block_idx] = true;
                }
            }
        }

        Ok(block_accessor)
    }

    /// Find the index of Handle Block that might contain the given primary key.
    fn find_common_handle_block_index(&self, pk: &[u8]) -> Result<Option<usize>> {
        // Binary search
        let index_block = self.get_handle_index_block()?;
        let n = index_block.handle_block_start_key.len();

        // Use byte order comparison for both integer and common PKs
        // Integer PKs are now encoded as BigEndian, so byte comparison works correctly
        let pos =
            crate::table::search(n, |i| index_block.handle_block_start_key[i].as_slice() > pk);

        if pos == 0 {
            return Ok(None);
        }
        Ok(Some(pos - 1))
    }

    fn find_int_handle_block_index(&self, pk: i64) -> Result<Option<usize>> {
        let index_block = self.get_handle_index_block()?;
        let n = index_block.handle_block_start_key.len();

        let pos = crate::table::search(n, |i| {
            let mut start_key = index_block.handle_block_start_key[i].as_slice();
            let start_key_int = start_key.get_i64();
            start_key_int > pk
        });

        if pos == 0 {
            return Ok(None);
        }
        Ok(Some(pos - 1))
    }
}

/// A zero-copy accessor for reading Handle Block.
#[derive(Clone)]
struct HandleBlockAccessor {
    data: Bytes,
    n_pk: u32,
    // Following fields are slices of data, do not own the data
    versions: &'static [u64],
    #[allow(unused)]
    delete_marks: &'static [u8],
    pk_common_offsets: &'static [u64],
    pk_data: Bytes,
}

impl HandleBlockAccessor {
    /// Parse Handle Block binary layout
    /// Handle Block structure:
    /// u32    Number of Primary Keys
    /// ...    Align to 8 bytes
    /// [u64]  Versions (mmap mapped)
    /// [u8]   Delete Mark (mmap mapped)
    /// ...    Align to 8 bytes
    /// [u32]  Common Handle Offsets (only exists for Common Handle, mmap
    /// mapped, has n+1 items) ...    Align to 8 bytes
    /// ...    Primary Key Data Bytes (mmap mapped)
    /// u32    Checksum (checksum of all above fields)
    /// ...    Align to 8 bytes
    fn new(mut data: Bytes, is_pk_int: bool) -> Result<Self> {
        use crate::codecutil::next_aligned_offset;

        let original_data = data.clone();

        // 1. Read PK count (u32)
        let n_pk = data.get_u32_le();
        if n_pk == 0 {
            bail!("Handle block cannot have zero PKs");
        }

        // 2. Skip padding to align to 8 bytes
        let mut current_offset = 4; // u32 for n_pk
        let next_offset = next_aligned_offset(current_offset, 8);
        if next_offset > current_offset {
            data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // 3. Read versions array [u64] (mmap mapped)
        let versions_size = (n_pk as usize) * 8;
        let versions_data = data.try_get_first(versions_size)?;
        let versions = versions_data.try_as_slice::<u64>()?;
        current_offset += versions_size;

        // 4. Read delete marks array [u8] (mmap mapped)
        let delete_marks_size = n_pk as usize;
        let delete_marks_data = data.try_get_first(delete_marks_size)?;
        let delete_marks = delete_marks_data.as_ref();
        current_offset += delete_marks_size;

        // 5. Align to 8 bytes
        let next_offset = next_aligned_offset(current_offset, 8);
        if next_offset > current_offset {
            data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // 6. Handle Common Handle Offsets [u64] (only exists for Common Handle, mmap
        //    mapped, has n+1 items)
        let (pk_common_offsets, pk_data_size) = if !is_pk_int {
            let offsets_size = ((n_pk + 1) as usize) * 8; // n_pk + 1 u64s
            let offsets_data = data.try_get_first(offsets_size)?;
            let offsets = offsets_data.try_as_slice::<u64>()?;
            current_offset += offsets_size;

            // 7. Align to 8 bytes
            let next_offset = next_aligned_offset(current_offset, 8);
            if next_offset > current_offset {
                data.advance(next_offset - current_offset);
                // current_offset no longer needs to be updated here, as it's
                // not used later
            }

            let pk_data_size = offsets[n_pk as usize] as usize;

            // Use unsafe extend_lifetime to avoid lifetime issues
            let offsets_static = unsafe { extend_lifetime(offsets) };
            (offsets_static, pk_data_size)
        } else {
            // For integer PK, no offset array, directly calculate data size
            (&[] as &[u64], (n_pk as usize) * 8) // Integer PK: u64 * n_pk
        };

        // 8. Read Primary Key Data Bytes (mmap mapped)
        let pk_data = data.try_get_first(pk_data_size)?;

        // Use unsafe extend_lifetime to avoid lifetime issues
        // This is safe because versions and delete_marks have the same lifetime as
        // original_data
        let versions_static = unsafe { extend_lifetime(versions) };
        let delete_marks_static = unsafe { extend_lifetime(delete_marks) };

        Ok(Self {
            data: original_data,
            n_pk,
            versions: versions_static,
            delete_marks: delete_marks_static,
            pk_common_offsets,
            pk_data,
        })
    }

    /// Verify the checksum of this Handle Block
    fn verify_checksum(&self, checksum_type: ChecksumType) -> Result<()> {
        if self.data.len() < 8 {
            bail!("Handle block data too short for checksum verification");
        }

        let data_len = self.data.len();
        if data_len < 8 {
            bail!("Handle block data too short for checksum");
        }

        // the checksum in handle block may be 8 bytes or 4 bytes.
        // the last 4 bytes will be 0 when padding was happend.
        let is_start = data_len - 4;
        let (content_for_checksum, stored_checksum) =
            if (&self.data[is_start..is_start + 4]).get_u32_le() == 0 {
                let start = data_len - 8;
                let content = &self.data[..start];
                let checksum = (&self.data[start..start + 4]).get_u32_le();
                (content, checksum)
            } else {
                let start = data_len - 4;
                let content = &self.data[..start];
                let checksum = (&self.data[start..start + 4]).get_u32_le();
                (content, checksum)
            };

        let calculated_checksum = checksum_type.checksum(content_for_checksum);

        if stored_checksum != calculated_checksum {
            bail!(
                "Handle block checksum mismatch: calculated {:#08x}, stored {:#08x}",
                calculated_checksum,
                stored_checksum
            );
        }

        Ok(())
    }

    fn int_pk_at(&self, i: usize) -> Result<Bytes> {
        if i >= self.n_pk as usize {
            bail!("PK index {} out of bounds, n_pk={}", i, self.n_pk);
        }

        // Integer PK: directly read the i-th i64 from pk_data
        let pks = self.pk_data.try_as_slice::<i64>()?;
        let pk_int = pks[i];
        Ok(Bytes::copy_from_slice(&pk_int.to_be_bytes()))
    }

    fn common_pk_at(&self, i: usize) -> Result<Bytes> {
        if i >= self.n_pk as usize {
            bail!("PK index {} out of bounds, n_pk={}", i, self.n_pk);
        }
        // Common PK: use offset array to extract data
        let start_offset = self.pk_common_offsets[i] as usize;
        let end_offset = self.pk_common_offsets[i + 1] as usize;

        if start_offset > end_offset || end_offset > self.pk_data.len() {
            bail!(
                "Invalid PK offsets: start={}, end={}, pk_data_len={}",
                start_offset,
                end_offset,
                self.pk_data.len()
            );
        }

        Ok(self.pk_data.slice(start_offset..end_offset))
    }

    fn version_at(&self, i: usize) -> Result<u64> {
        Ok(self.versions[i])
    }

    #[allow(unused)]
    fn delete_mark_at(&self, i: usize) -> Result<bool> {
        // In delete_marks array, 0 means not deleted, non-0 means deleted
        Ok(self.delete_marks[i] != 0)
    }

    /// Search for newer versions within this Handle Block for integer PK.
    fn has_newer_version_int(&self, pk: i64, version: u64, max_version: u64) -> Result<bool> {
        // For integer PK, can directly use helper function
        has_newer_version(
            pk,
            version,
            max_version,
            self.n_pk as usize,
            |i| self.version_at(i),
            |i| {
                let pks = self.pk_data.try_as_slice::<i64>()?;
                Ok(pks[i])
            },
        )
    }

    /// Search for newer versions within this Handle Block for common PK.
    fn has_newer_version_common(&self, pk: &[u8], version: u64, max_version: u64) -> Result<bool> {
        use std::cmp::Ordering;

        // For common PK, directly implement binary search logic
        let n = self.n_pk as usize;
        let pos = crate::table::try_search::<anyhow::Error>(n, |i| {
            let pk_at_i = self.common_pk_at(i)?;
            match pk_at_i.as_ref().cmp(pk) {
                Ordering::Less => Ok(false),
                Ordering::Greater => Ok(true),
                Ordering::Equal => Ok(self.version_at(i)? <= max_version),
            }
        })?;

        if pos >= n {
            return Ok(false);
        }

        let pk_at_pos = self.common_pk_at(pos)?;
        if pk_at_pos.as_ref() == pk {
            let version_at_pos = self.version_at(pos)?;
            // We already know version_at_pos <= max_version (from search condition)
            // Only need to check version_at_pos > version
            if version_at_pos > version {
                return Ok(true);
            }
        }

        Ok(false)
    }
}

/// Find whether there is a newer version of a specified PK.
/// This function is the same as the implementation in PackedFile, used for
/// binary search within Handle Block
fn has_newer_version<PK: Ord>(
    target_pk: PK,
    target_version: u64,
    target_max_version: u64,
    n: usize,
    version_at: impl Fn(usize) -> Result<u64>,
    pk_at: impl Fn(usize) -> Result<PK>,
) -> Result<bool> {
    use std::cmp::Ordering;

    // Find the first position `pos` such that:
    // `pk_at(pos) >= target_pk` and `version_at(pos) <= target_max_version`
    let pos = crate::table::try_search::<anyhow::Error>(n, |i| match pk_at(i)?.cmp(&target_pk) {
        Ordering::Less => Ok(false),
        Ordering::Greater => Ok(true),
        Ordering::Equal => Ok(version_at(i)? <= target_max_version),
    })?;

    if pos >= n {
        return Ok(false);
    }

    let pk = pk_at(pos)?;
    if pk == target_pk {
        let version = version_at(pos)?;
        // We already know version <= target_max_version (from search condition)
        // Only need to check version > target_version
        if version > target_version {
            return Ok(true);
        }
    }

    Ok(false)
}

/// Unsafe function to extend lifetime
/// This is the same as the implementation in PackedFile, used to handle
/// lifetime issues
unsafe fn extend_lifetime<'b, T: ?Sized>(r: &'b T) -> &'static T {
    std::mem::transmute::<&'b T, &'static T>(r)
}
