use std::{collections::BTreeMap, io::<PERSON><PERSON><PERSON>, sync::Arc};

use anyhow::Result;

use super::*;
use crate::{ia::manager::tests::IaFileTester, table::ChecksumType};

pub const SCORE_SAMPLE_DOCS: &[&str] = &[
    // 0
    "machine learning machine learning machine learning",
    // 1
    "machine learning algorithms. Machine learning models. Machine learning optimization.",
    // 2
    "Machine learning (Machine learning (Machine learning (Machine learning (Machine learning (is used in AI. Machine learning (Machine learning (Machine learning (Machine learning (Machine learning (is used in AI. Machine learning (Machine learning (Machine learning (Machine learning (Machine learning (is used in AI. Machine learning ends.",
    // 3
    "Understanding machine learning: basics of learning from machines. Machine learning requires data.",
    // 4
    "machine learning",
    // 5
    "This 50-word document briefly mentions machine learning once. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. Generic text about AI. ",
    // 6
    "Learning machine learning machine. Learning machine applications.",
    // 7
    "Space Tourism: Risks and Opportunities - Private companies like SpaceX and Blue Origin are making suborbital flights accessible, but safety protocols remain a critical concern.",
    // 8
    "Sustainable Fashion Trends - Eco-friendly materials like mushroom leather and recycled polyester are reshaping the fashion industry's environmental impact.",
    // 9
    "AI-powered tools are transforming medical imaging analysis. Deep learning algorithms now detect early-stage tumors with 95% accuracy.",
];

#[cfg(test)]
impl DedicatedFile<()> {
    #[cfg(test)]
    pub async fn from_buffer(buf: &[u8]) -> Result<(IaFileTester, DedicatedFile<()>)> {
        let tester = IaFileTester::default();
        let ia_file = tester
            .new_ia_file(
                crate::dfs::FileType::FtsDedicatedFile,
                buf,
                crate::table::fts::test_util::get_dedicated_file_meta_offset(buf),
            )
            .await;
        let file = Arc::new(ia_file);
        Ok((tester, DedicatedFile::new(file)?))
        // tester must be placed as left so that it can be dropped after
        // packed_file
    }
}

#[cfg(test)]
impl HandleBlockAccessor {
    #[cfg(test)]
    fn get_common_handle_idx(&self, handle: &[u8]) -> Result<usize> {
        if self.n_pk == 0 {
            bail!("Handle block is empty");
        }

        // Optimization for integer PK: if data is contiguous, can directly use PK value
        // as index
        if self.is_pk_int && handle.len() == 8 {
            let target_pk = u64::from_be_bytes([
                handle[0], handle[1], handle[2], handle[3], handle[4], handle[5], handle[6],
                handle[7],
            ]);

            // Check if within range
            if target_pk < self.n_pk as u64 {
                // Verify if PK at this index matches
                let pk_at_target = self.common_pk_at(target_pk as usize)?;
                if pk_at_target.as_ref() == handle {
                    return Ok(target_pk as usize);
                }
            }
        }

        let n = self.n_pk as usize;
        let pos = crate::table::try_search::<anyhow::Error>(n, |i| {
            let pk_at_i = self.common_pk_at(i)?;
            Ok(pk_at_i.as_ref() >= handle)
        })?;

        // Check if we found an exact match
        if pos < n {
            let pk_at_pos = self.common_pk_at(pos)?;
            if pk_at_pos.as_ref() == handle {
                return Ok(pos);
            }
        }

        // If no exact match found, return error
        bail!("Handle not found in this handle block");
    }

    #[cfg(test)]
    fn get_int_handle_idx(&self, handle: i64) -> Result<usize> {
        if self.n_pk == 0 {
            bail!("Handle block is empty");
        }

        let n = self.n_pk as usize;
        let pos = crate::table::try_search::<anyhow::Error>(n, |i| {
            let mut pk_at_i = self.int_pk_at(i)?;
            let pk_int = pk_at_i.get_i64();
            Ok(pk_int >= handle)
        })?;

        // Check if we found an exact match
        if pos < n {
            let mut pk_at_pos = self.int_pk_at(pos)?;
            let pk_int = pk_at_pos.get_i64();
            if pk_int == handle {
                return Ok(pos);
            }
        }

        // If no exact match found, return error
        bail!("Handle not found in this handle block");
    }
}

#[tokio::test(flavor = "multi_thread")]
// Insert multiple handles so that DedicatedFile has multiple handle blocks,
// verify the count of handle_index_block_offset.
async fn test_finish_handle_block() -> Result<()> {
    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        Cursor::new(&mut buffer),
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024, /* Use 1MB small block size to force creation of
                                             * multiple blocks */
            checksum_type: ChecksumType::Crc32,
        },
        true,
        b"test_lp",
    )?;

    // Add enough data to trigger handle block flushing
    // Each integer PK requires approximately 8 + 8 + 1 = 17 bytes (PK + version +
    // delete_mark) Plus other overhead, we add 100,000 PKs to exceed the
    // 1MB limit, ensuring multiple handle blocks are created
    for i in 0..100000 {
        builder.add_pk_int(i, 1000, false)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    let footer = dedicated_file.get_footer();
    assert_eq!(footer.magic, FTS_DEDICATED_FILE_MAGIC);
    assert_eq!(footer.format, FTS_DEDICATED_FILE_FORMAT_V1);

    // Verify other metadata block offsets
    assert!(footer.pk_filter_block_offset > footer.handle_index_block_offset);
    assert!(footer.prop_offset > footer.pk_filter_block_offset);

    // Verify properties can be loaded
    let props = dedicated_file.get_props();
    assert!(props.get_is_int_handle());
    assert_eq!(props.get_lp_key(), b"test_lp");

    // Core test: verify creation of multiple handle blocks
    // Load handle index block to check the count of handle_block_offsets
    let handle_index_block = dedicated_file.get_handle_index_block()?;

    // Verify the handle_block_offsets array
    let handle_block_offsets = &handle_index_block.handle_block_offsets;
    println!("Handle block offsets count: {}", handle_block_offsets.len());
    println!("Handle block offsets: {:?}", handle_block_offsets);

    // Since we added 100,000 PKs, each approximately 17 bytes, plus overhead,
    // the total data size is approximately 1.7MB. Therefore, multiple handle blocks
    // should be created (each block is limited to 1MB). At least 2 handle
    // blocks are expected.
    assert!(
        handle_block_offsets.len() >= 2,
        "Expected at least 2 handle block offsets (multiple blocks), got {}",
        handle_block_offsets.len()
    );

    // Verify that the offset is increasing
    for i in 1..handle_block_offsets.len() {
        assert!(
            handle_block_offsets[i] > handle_block_offsets[i - 1],
            "Handle block offsets should be increasing: offset[{}]={} should be > offset[{}]={}",
            i,
            handle_block_offsets[i],
            i - 1,
            handle_block_offsets[i - 1]
        );
    }

    // Verify that the first offset is 0 (the start of the data block)
    assert_eq!(
        handle_block_offsets[0], 0,
        "First handle block offset should be 0"
    );

    // Verify that the last offset should equal the start of the handle index block
    // (because the N+1 offset marks the end of the last handle block)
    let last_offset = handle_block_offsets[handle_block_offsets.len() - 1];
    assert_eq!(
        last_offset, footer.handle_index_block_offset,
        "Last handle block offset {} should equal handle_index_block_offset {} (N+1 offset array)",
        last_offset, footer.handle_index_block_offset
    );

    // Verify that the size between each handle block is reasonable
    for i in 1..handle_block_offsets.len() - 1 {
        let block_size = handle_block_offsets[i] - handle_block_offsets[i - 1];
        // Each handle block should be close to or exceed the 1MB limit
        assert!(
            block_size >= 1024 * 1024,
            "Handle block {} size {} should be close to the 1MB limit",
            i - 1,
            block_size
        );
    }

    println!(
        "Successfully verified {} handle blocks",
        handle_block_offsets.len()
    );

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_multi_block_int_handle_search() -> Result<()> {
    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        Cursor::new(&mut buffer),
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024, // 1MB block size
            checksum_type: ChecksumType::Crc32,
        },
        true,
        b"test_lp",
    )?;

    // Add enough data to create multiple handle blocks
    // Each integer PK approximately requires 8 + 8 + 1 = 17 bytes (PK + version +
    // delete_mark) Add 150,000 PKs to ensure multiple blocks are created,
    // totaling about 2.5MB of data
    for i in 0..150000 {
        builder.add_pk_int(i, 100, false)?; // One delete mark per 1000 PKs
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    // Verify footer can be parsed correctly
    let footer = dedicated_file.get_footer();

    let handle_in_range: i64 = 10000;
    let handle_out_range: i64 = 1500001;

    let pk_filter = dedicated_file.get_pk_filter()?;

    // PK filter needs to be checked with hash values
    let handle_in_range_hash = farmhash::fingerprint64(&handle_in_range.to_be_bytes());
    let handle_out_range_hash = farmhash::fingerprint64(&handle_out_range.to_be_bytes());

    assert!(
        pk_filter.contains(&handle_in_range_hash),
        "PK {} should be in the filter",
        handle_in_range
    );
    assert!(
        !pk_filter.contains(&handle_out_range_hash),
        "PK {} should not be in the filter",
        handle_out_range
    );

    // Step 1: Use binary search to find the handle block containing handle_in_range
    let block_idx = dedicated_file.find_int_handle_block_index(handle_in_range)?;

    assert!(
        block_idx.is_some(),
        "Should find handle block containing PK {}",
        handle_in_range
    );
    let block_idx = block_idx.unwrap();
    println!("Found PK {} in handle block {}", handle_in_range, block_idx);

    let handle_block_accessor =
        DedicatedFile::load_handle_block_by_idx(&dedicated_file.0.file, footer, block_idx)?;

    // Step 4: Use binary search to find the PK's index within the handle block
    let handle_idx = handle_block_accessor.get_int_handle_idx(handle_in_range)?;
    println!(
        "Found index {} for PK {} in handle block {}",
        handle_idx, handle_in_range, block_idx
    );
    let mut pk_bytes = handle_block_accessor.int_pk_at(handle_idx)?;
    let pk = pk_bytes.get_i64();
    let ve = handle_block_accessor.version_at(handle_idx)?;
    let dm = handle_block_accessor.delete_mark_at(handle_idx)?;

    assert!(
        pk == handle_in_range,
        "PK mismatch: Expected {}, Got {}",
        handle_in_range,
        pk
    );
    assert!(ve == 100, "Version mismatch: Expected {}, Got {}", 100, ve);
    assert!(!dm, "Delete mark mismatch: Expected {}, Got {}", false, dm);

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_multi_block_int_handle_search_with_negative_positive() -> Result<()> {
    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        Cursor::new(&mut buffer),
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024, // 1MB block size
            checksum_type: ChecksumType::Crc32,
        },
        true,
        b"test_lp_mixed",
    )?;

    // Add mixed negative and positive integer PKs in ascending order
    // Range: -75000 to +74999 (total 150,000 PKs)
    // This ensures we have both negative and positive values
    for i in -75000i64..75000i64 {
        builder.add_pk_int(i, 100, i % 1000 == 0)?; // Mark every 1000th PK as deleted
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    // Test cases with both negative and positive handles
    let test_cases = vec![
        (-50000i64, "large negative"),
        (-1000i64, "small negative"),
        (-1i64, "negative one"),
        (0i64, "zero"),
        (1i64, "positive one"),
        (1000i64, "small positive"),
        (50000i64, "large positive"),
    ];

    let pk_filter = dedicated_file.get_pk_filter()?;

    for (handle_value, description) in test_cases {
        println!("Testing {} handle: {}", description, handle_value);

        // Test PK filter
        let handle_hash = farmhash::fingerprint64(&handle_value.to_be_bytes());
        assert!(
            pk_filter.contains(&handle_hash),
            "PK {} ({}) should be in the filter",
            handle_value,
            description
        );

        // Find handle block containing this PK
        let block_idx = dedicated_file.find_int_handle_block_index(handle_value)?;

        assert!(
            block_idx.is_some(),
            "Should find handle block containing PK {} ({})",
            handle_value,
            description
        );
        let block_idx = block_idx.unwrap();
        println!(
            "Found PK {} ({}) in handle block {}",
            handle_value, description, block_idx
        );

        // Load the handle block
        let footer = dedicated_file.get_footer();
        let handle_block_accessor =
            DedicatedFile::load_handle_block_by_idx(&dedicated_file.0.file, footer, block_idx)?;

        // Find the PK's index within the handle block
        let handle_idx = handle_block_accessor.get_int_handle_idx(handle_value)?;
        println!(
            "Found index {} for PK {} ({}) in handle block {}",
            handle_idx, handle_value, description, block_idx
        );

        // Verify the PK data
        let mut pk_bytes = handle_block_accessor.int_pk_at(handle_idx)?;
        let pk = pk_bytes.get_i64();
        let version = handle_block_accessor.version_at(handle_idx)?;
        let delete_mark = handle_block_accessor.delete_mark_at(handle_idx)?;

        // Verify correctness
        assert_eq!(
            pk, handle_value,
            "PK mismatch for {}: Expected {}, Got {}",
            description, handle_value, pk
        );
        assert_eq!(
            version, 100,
            "Version mismatch for {}: Expected 100, Got {}",
            description, version
        );

        let expected_delete = handle_value % 1000 == 0;
        assert_eq!(
            delete_mark, expected_delete,
            "Delete mark mismatch for {}: Expected {}, Got {}",
            description, expected_delete, delete_mark
        );

        println!(
            "✅ PK {} ({}) verified successfully",
            handle_value, description
        );
    }

    // Test boundary cases
    let boundary_cases = vec![(-75000i64, "minimum value"), (74999i64, "maximum value")];

    for (handle_value, description) in boundary_cases {
        println!("Testing boundary case {}: {}", description, handle_value);

        let block_idx = dedicated_file.find_int_handle_block_index(handle_value)?;

        assert!(
            block_idx.is_some(),
            "Should find handle block containing boundary PK {} ({})",
            handle_value,
            description
        );

        println!(
            "✅ Boundary case {} ({}) found successfully",
            handle_value, description
        );
    }

    // Test out-of-range cases
    let out_of_range_cases = vec![(-100000i64, "below minimum"), (100000i64, "above maximum")];

    for (handle_value, description) in out_of_range_cases {
        println!(
            "Testing out-of-range case {}: {}",
            description, handle_value
        );

        let handle_hash = farmhash::fingerprint64(&handle_value.to_be_bytes());
        assert!(
            !pk_filter.contains(&handle_hash),
            "Out-of-range PK {} ({}) should not be in the filter",
            handle_value,
            description
        );

        println!(
            "✅ Out-of-range case {} ({}) correctly filtered out",
            handle_value, description
        );
    }

    println!("🎉 All mixed negative/positive integer handle tests passed!");
    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
// Test common handle search scenarios
async fn test_multi_block_common_handle_search() -> Result<()> {
    // Step 1: Build test data - using common handle (non-integer PK)
    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        &mut buffer,
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024, // 1MB handle block size
            checksum_type: ChecksumType::Crc32,
        },
        false, // is_int_handle = false for common handle
        b"test_lp",
    )?;

    // Generate common handles sorted in byte order
    // Use formatted strings to ensure correct byte order
    let mut handles = Vec::new();
    for i in 0..100000u64 {
        // Use fixed-length string format to ensure ascending byte order
        let handle = format!("handle_{:08}", i); // "handle_00000000", "handle_00000001", ...
        handles.push(handle);
    }

    // Verify handles are sorted in byte order
    for i in 1..handles.len() {
        assert!(
            handles[i - 1].as_bytes() < handles[i].as_bytes(),
            "Handles must be in byte order: {} >= {}",
            handles[i - 1],
            handles[i]
        );
    }

    // Add all PKs to the builder
    for handle in handles.iter() {
        builder.add_pk_common(handle.as_bytes(), 100, false)?; // version=100, delete_mark=false
    }

    // Finish building
    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    // Step 2: Create IaFile and DedicatedFile
    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    let footer = dedicated_file.get_footer();

    // Step 3: Load various index structures
    let pk_filter = dedicated_file.get_pk_filter()?;
    let handle_block_index = dedicated_file.get_handle_index_block()?;

    println!("Common handle test data built:");
    println!("  Total PKs: {}", handles.len());
    println!(
        "  Number of Handle blocks: {}",
        handle_block_index.handle_block_offsets.len() - 1
    );

    // Step 4: Choose PKs for testing
    let handle_in_range = "handle_00010000"; // Existing handle
    let handle_out_range = "handle_99999999"; // Non-existing handle (out of range)

    // Step 5: Check if PK might exist via PK filter
    let handle_in_range_hash = farmhash::fingerprint64(handle_in_range.as_bytes());
    let handle_out_range_hash = farmhash::fingerprint64(handle_out_range.as_bytes());

    assert!(
        pk_filter.contains(&handle_in_range_hash),
        "PK {} should be in the filter",
        handle_in_range
    );
    assert!(
        !pk_filter.contains(&handle_out_range_hash),
        "PK {} should not be in the filter",
        handle_out_range
    );

    // Step 6: Use binary search to find the handle block containing handle_in_range
    let handle_in_range_bytes = handle_in_range.as_bytes();
    let block_idx = dedicated_file.find_common_handle_block_index(handle_in_range_bytes)?;

    assert!(
        block_idx.is_some(),
        "Should find handle block containing PK {}",
        handle_in_range
    );
    let block_idx = block_idx.unwrap();
    println!("Found PK {} in handle block {}", handle_in_range, block_idx);

    // Step 7: Calculate handle block offset and size
    let block_start_offset = handle_block_index.handle_block_offsets[block_idx];
    let block_end_offset = handle_block_index.handle_block_offsets[block_idx + 1];
    let block_size = (block_end_offset - block_start_offset) as usize;

    println!(
        "Handle block {} offsets: {} - {}, size: {} bytes",
        block_idx, block_start_offset, block_end_offset, block_size
    );

    // Step 8: Load handle block accessor
    let handle_block_accessor =
        DedicatedFile::load_handle_block_by_idx(&dedicated_file.0.file, footer, block_idx)?;

    // Step 9: Use binary search to find the PK's index within the handle block
    let handle_idx = handle_block_accessor.get_common_handle_idx(handle_in_range_bytes)?;
    println!(
        "Found index {} for PK {} in handle block {}",
        handle_idx, handle_in_range, handle_idx
    );

    // Step 10: Get version and delete_mark at this index
    let found_version = handle_block_accessor.version_at(handle_idx)?;
    let found_delete_mark = handle_block_accessor.delete_mark_at(handle_idx)?;

    // Step 11: Verify the found results
    let version = found_version;
    let delete_mark = found_delete_mark;

    // Verify results match expectations
    assert_eq!(
        version, 100,
        "Version mismatch for PK {}: Expected {}, Got {}",
        handle_in_range, 100, version
    );
    assert_eq!(
        delete_mark, false,
        "Delete mark mismatch for PK {}: Expected {}, Got {}",
        handle_in_range, false, delete_mark
    );

    println!(
        "✓ Successfully found information for PK {}: version={}, delete_mark={}",
        handle_in_range, version, delete_mark
    );

    // Step 12: Additional test: verify non-existing PK
    println!("Verifying non-existing PK {}", handle_out_range);

    // Attempt to find a non-existing PK, should return error or None
    let out_block_idx =
        dedicated_file.find_common_handle_block_index(handle_out_range.as_bytes())?;
    if out_block_idx.is_none() {
        println!(
            "✓ PK {} has no corresponding handle block found (as expected)",
            handle_out_range
        );
    } else {
        // If a potential block is found, need to verify if the index is valid
        let out_block_idx = out_block_idx.unwrap();
        let max_block_idx = handle_block_index.handle_block_offsets.len() - 1; // Max valid index

        if out_block_idx > max_block_idx {
            println!(
                "✓ Handle block index {} for PK {} is out of bounds (as expected)",
                out_block_idx, handle_out_range
            );
        } else {
            // Index is valid, attempt to search in that block
            let out_handle_block_accessor = DedicatedFile::load_handle_block_by_idx(
                &dedicated_file.0.file,
                footer,
                out_block_idx,
            )?;
            let out_search_result =
                out_handle_block_accessor.get_common_handle_idx(handle_out_range.as_bytes());
            assert!(
                out_search_result.is_err(),
                "Should not find non-existing PK {}",
                handle_out_range
            );
            println!(
                "✓ Confirmed PK {} does not exist in handle block {}",
                handle_out_range, out_block_idx
            );
        }
    }

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_pk_filter() -> Result<()> {
    // Test integer PK construction and PK filter functionality
    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        Cursor::new(&mut buffer),
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024, // 1MB block size
            checksum_type: ChecksumType::Crc32,
        },
        true,
        b"test_lp",
    )?;

    // Add some test data, ensure enough data to test PK filter
    for i in (0..10000).step_by(10) {
        // Add 1000 PKs: 0, 10, 20, 30, ..., 9990
        builder.add_pk_int(i, 50000 - i as u64, i % 100 == 0)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    let pk_filter = dedicated_file.get_pk_filter()?;

    // Test existing PK (should pass filter)
    let existing_pk_hash = farmhash::fingerprint64(&0u64.to_be_bytes());
    assert!(
        pk_filter.contains(&existing_pk_hash),
        "PK 0 should exist in filter"
    );

    let existing_pk_hash = farmhash::fingerprint64(&9990u64.to_be_bytes());
    assert!(
        pk_filter.contains(&existing_pk_hash),
        "PK 9990 should exist in filter"
    );

    let not_existing_pk_hash = farmhash::fingerprint64(&99900u64.to_be_bytes());
    assert!(
        !pk_filter.contains(&not_existing_pk_hash),
        "PK 99900 should not exist in filter"
    );

    // Test common PK construction
    let mut buffer2 = Vec::new();
    let mut builder2 = builder::DedicatedFileBuilder::new(
        Cursor::new(&mut buffer2),
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024,
            checksum_type: ChecksumType::Crc32,
        },
        false,
        b"test_lp_common",
    )?;

    // Add some common PK test data
    let test_keys: Vec<&[u8]> = vec![
        b"apple",
        b"banana",
        b"cherry",
        b"date",
        b"elderberry",
        b"fig",
        b"grape",
        b"honeydew",
        b"kiwi",
        b"lemon",
    ];
    for (i, &key) in test_keys.iter().enumerate() {
        builder2.add_pk_common(key, 1000 - i as u64, i % 3 == 0)?;
    }

    let tantivy_files = BTreeMap::new();
    builder2.finish(tantivy_files)?;

    let (_g, dedicated_file2) = DedicatedFile::from_buffer(&buffer2).await?;

    // Verify PK filter was correctly created
    let pk_filter = dedicated_file2.get_pk_filter()?;

    // Test existing PK (should pass filter)
    let existing_pk_hash = farmhash::fingerprint64(b"apple");
    assert!(
        pk_filter.contains(&existing_pk_hash),
        "PK apple should exist in filter"
    );

    let existing_pk_hash = farmhash::fingerprint64(b"lemon");
    assert!(
        pk_filter.contains(&existing_pk_hash),
        "PK lemon should exist in filter"
    );

    let not_existing_pk_hash = farmhash::fingerprint64(b"orange");
    assert!(
        !pk_filter.contains(&not_existing_pk_hash),
        "PK orange should not exist in filter"
    );

    println!("Successfully verified common PK file construction");

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_handle_block_checksum_corruption() -> Result<()> {
    // This test verifies checksum mechanism and data integrity checks
    // Build a valid DedicatedFile with checksum enabled
    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        &mut buffer,
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024, // 1MB block size
            checksum_type: ChecksumType::Crc32,
        },
        true, // Integer PK
        b"test_lp_checksum",
    )?;

    // Add some test data
    for i in 0..1000 {
        builder.add_pk_int(i, 10000 - i as u64, i % 50 == 0)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    // Test Case 1: Handle Block Corruption
    {
        let mut corrupted_buffer = buffer.clone();

        // Corrupt a byte in the handle block area (near the beginning)
        corrupted_buffer[20] ^= 0xFF;

        // DedicatedFile::new() should succeed because metadata and footer are intact
        let (_g, dedicated_file) = DedicatedFile::from_buffer(&corrupted_buffer).await?;

        // Accessing the corrupted handle block should fail
        let err = dedicated_file
            .has_newer_version_int(0, 50, 200)
            .unwrap_err();

        assert!(err.to_string().contains("Handle block checksum mismatch"));
    }

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_footer_marshal_unmarshal_with_checksum() {
    let mut f = DedicatedFileFooter::new();
    f.checksum_type = ChecksumType::Crc32c;
    f.checksum_other_meta = 0xA1B2_C3D4;
    f.data_block_offset = 0x10_20_30_40;
    f.pk_filter_block_offset = 0x20_30_40_50;
    f.handle_index_block_offset = 0x30_40_50_60;
    f.prop_offset = 0x40_50_60_70;

    let mut buf = Vec::new();
    f.marshal(&mut buf).unwrap();

    // Roundtrip test - unmarshal and verify all fields match
    let f2 = DedicatedFileFooter::unmarshal(&buf).unwrap();
    assert_eq!(f2.format, f.format);
    assert_eq!(f2.checksum_type.value(), f.checksum_type.value());
    assert_eq!(f2.checksum_other_meta, f.checksum_other_meta);
    assert_eq!(f2.data_block_offset, f.data_block_offset);
    assert_eq!(f2.pk_filter_block_offset, f.pk_filter_block_offset);
    assert_eq!(f2.handle_index_block_offset, f.handle_index_block_offset);
    assert_eq!(f2.prop_offset, f.prop_offset);
    assert_eq!(f2.magic, f.magic);

    // Modify checksum field should cause checksum mismatch
    let mut buf2 = buf.clone();
    buf2[8] ^= 0xFF; // Flip a bit in checksum field
    let result = DedicatedFileFooter::unmarshal(&buf2);
    assert!(
        result.is_err(),
        "Expected checksum mismatch when checksum field is corrupted"
    );
    let error_msg = result.unwrap_err().to_string();
    assert!(
        error_msg.contains("FtsDedicatedFile footer checksum mismatch"),
        "Error should mention checksum mismatch"
    );
}

#[tokio::test(flavor = "multi_thread")]
async fn test_mmap_fts_search() -> Result<()> {
    println!("🧪 Testing mmap-based FTS search functionality...");

    // Step 1: Build a DedicatedFile containing the Tantivy index.
    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        std::io::Cursor::new(&mut buffer),
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024,
            checksum_type: ChecksumType::Crc32,
        },
        true,
        b"test_mmap_search",
    )?;

    let mut index_writer = clara_fts::IndexWriterInMemory::new("STANDARD_V1")?;

    // Add test documents.
    for (i, doc) in SCORE_SAMPLE_DOCS.iter().enumerate() {
        builder.add_pk_int(i as i64, 100, false)?;
        index_writer.add_document(doc)?;
    }

    // Parse index_bytes and create tantivy_files.
    let index_bytes = index_writer.finalize()?;
    let decoder = clara_fts::MergedFileDecoder::from_buffer(index_bytes)?;
    let mut tantivy_files = std::collections::BTreeMap::new();

    for file_path in decoder.list_files() {
        if let Some(file_name) = file_path.file_name() {
            if let Some(file_name_str) = file_name.to_str() {
                let key_option: Option<String> = match file_name_str {
                    "meta.json" => Some("json0".to_string()),
                    ".managed.json" => Some("json1".to_string()),
                    name if name.ends_with(".term") => Some("term".to_string()),
                    name if name.ends_with(".idx") => Some("idx".to_string()),
                    name if name.ends_with(".pos") => Some("pos".to_string()),
                    name if name.ends_with(".store") => Some("store".to_string()),
                    name if name.ends_with(".fast") => Some("fast".to_string()),
                    name if name.ends_with(".fieldnorm") => Some("fieldnorm".to_string()),
                    _ => None,
                };

                if let Some(key) = key_option {
                    if let Some(owned_bytes) = decoder.get(&file_path) {
                        let file_content = owned_bytes.as_slice().to_vec();
                        tantivy_files.insert(key, file_content);
                    }
                }
            }
        }
    }

    // Finish building.
    builder.finish(tantivy_files)?;
    println!(
        "✅ DedicatedFile built successfully with {} bytes",
        buffer.len()
    );

    // Step 2: Create a DedicatedFile instance.
    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    // Step 3: Test the search functionality.
    let mut fts_query = tipb::FtsQueryInfo::new();
    fts_query.set_query_text("machine learning".to_string());
    fts_query.set_query_tokenizer("STANDARD_V1".to_string());
    fts_query.set_query_type(tipb::FtsQueryType::FtsQueryTypeWithScore);

    println!("🔍 Executing mmap-based search for 'machine learning'...");

    let results = dedicated_file.search(&fts_query).await?;

    println!("✅ Search completed!");
    println!("   - Found {} results", results.len());

    if !results.is_empty() {
        let expected_doc = [0, 1, 2, 3, 4, 5, 6, 9];
        assert!(results.len() == expected_doc.len());
        println!("📊 Search Results:");
        for (i, result) in results.iter().enumerate() {
            assert!(expected_doc[i] == result.doc_id);
        }
    }

    // Step 4: Test the caching mechanism - the second search should use the cache.
    println!("🔍 Testing cache mechanism with second search...");
    let cached_results = dedicated_file.search(&fts_query).await?;

    // Verify that the cached results are the same as the first run.
    assert_eq!(
        cached_results.len(),
        results.len(),
        "Cached results should match"
    );
    for (i, result) in cached_results.iter().enumerate() {
        assert_eq!(result.doc_id, results[i].doc_id);
        assert_eq!(result.score, results[i].score);
    }

    println!("✅ Mmap-based FTS search test completed successfully!");
    println!("   - Mmap Directory implementation working correctly");
    println!("   - Both scored and no-score searches functional");
    println!("   - IndexReader caching mechanism working");
    println!("   - Document content extraction working");
    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_last_int_handle_block_access() -> Result<()> {
    // This test specifically targets the bug where accessing the last handle block
    // would cause an out-of-bounds error due to incorrect N vs N+1 offset array
    // handling

    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        Cursor::new(&mut buffer),
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024, // Small block size to force multiple blocks
            checksum_type: ChecksumType::Crc32,
        },
        true, // Integer PK
        b"test_lp",
    )?;

    // Add enough data to create multiple handle blocks
    // With small block size, this should create several blocks
    for i in 0..1000 {
        builder.add_pk_int(i, 200, false)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    // Create IaFile from buffer using the same pattern as other tests
    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    // Test PKs that should definitely be in the last block
    // We need to test PKs that are >= last_block_start_pk
    let test_cases = vec![
        990i64, // First PK in last block
        995i64, // Second PK in last block (if exists)
        999i64, // Highest PK we added (should be in last block)
    ];

    let handle_index = dedicated_file.get_handle_index_block()?;

    for pk in test_cases {
        let Some(idx) = dedicated_file.find_int_handle_block_index(pk)? else {
            bail!("Should find handle block containing PK {:?}", pk);
        };
        assert_eq!(idx, handle_index.handle_block_start_key.len() - 1);

        // This call should not panic or cause out-of-bounds errors
        let result = dedicated_file.has_newer_version_int(pk, 50, 200);
        assert!(result.is_ok());
        assert!(result.unwrap());

        let result = dedicated_file.has_newer_version_int(pk, 300, 200);
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }
    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_last_common_handle_block_access() -> Result<()> {
    // This test specifically targets the bug where accessing the last handle block
    // would cause an out-of-bounds error due to incorrect N vs N+1 offset array
    // handling This version tests common handles (string PKs) instead of
    // integer PKs

    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        &mut buffer, // Use &mut buffer instead of Cursor
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024, // Use larger block size like existing tests
            checksum_type: ChecksumType::Crc32,
        },
        false, // Common Handle (string PK)
        b"test_lp",
    )?;

    // Add enough data to create multiple handle blocks
    // Use string PKs with consistent format for proper lexicographic ordering
    // Use fewer PKs but still enough to create multiple blocks
    for i in 0..10000 {
        let pk_str = format!("handle_{:08}", i); // e.g., "handle_00000000", "handle_00000001"
        builder.add_pk_common(pk_str.as_bytes(), 200, false)?;
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    // Create IaFile from buffer using the same pattern as other tests
    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    // Test PKs that should definitely be in the last block
    // For common handles, we need to be more careful about which PKs are in the
    // last block Let's test PKs that are very likely to be in the last few
    // blocks
    let test_cases = vec![
        b"handle_00009997".to_vec(), // Near the end
        b"handle_00009998".to_vec(), // Near the end
        b"handle_00009999".to_vec(), // Highest PK we added (should be in last block)
    ];

    let handle_index = dedicated_file.get_handle_index_block()?;

    for pk_bytes in test_cases {
        let Some(idx) = dedicated_file.find_common_handle_block_index(&pk_bytes)? else {
            bail!("Should find handle block containing PK {:?}", pk_bytes);
        };
        assert_eq!(idx, handle_index.handle_block_start_key.len() - 1);

        // This call should not panic or cause out-of-bounds errors
        let result = dedicated_file.has_newer_version_common(&pk_bytes, 50, 200);
        assert!(result.is_ok());
        assert!(result.unwrap());

        let result = dedicated_file.has_newer_version_common(&pk_bytes, 300, 200);
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }
    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_handle_block_fifo_cache() -> Result<()> {
    // Create a DedicatedFile with many handle blocks to test FIFO cache behavior
    let mut buffer = Vec::new();
    let mut builder = builder::DedicatedFileBuilder::new(
        Cursor::new(&mut buffer),
        builder::DedicatedFileBuilderOptions {
            handle_block_size: 256, // Small block size to force multiple blocks
            checksum_type: ChecksumType::Crc32,
        },
        true, // Use integer handles for simplicity
        b"test_lp_key",
    )?;

    // Add enough entries to create more than 10 handle blocks
    // Each handle block typically contains multiple entries, so we need many
    // entries
    for i in 0..10000 {
        let pk = i as i64; // Use i64 for integer PKs
        let version = 100 + i as u64;
        builder.add_pk_int(pk, version, false)?; // false = not deleted
    }

    let tantivy_files = BTreeMap::new();
    builder.finish(tantivy_files)?;

    // Create IaFile and DedicatedFile
    let (_g, dedicated_file) = DedicatedFile::from_buffer(&buffer).await?;

    // Initially cache should be empty
    assert_eq!(dedicated_file.handle_block_cache_size(), 0);

    // Access handle blocks by searching for different PKs
    // This should trigger loading of different handle blocks
    let test_pks = vec![
        10i64, 500i64, 1000i64, 1500i64, 2000i64, 3000i64, 4000i64, 5000i64, 6000i64, 7000i64,
    ];
    let mut pks_block_idx = Vec::new();

    for &pk_val in &test_pks {
        let idx = dedicated_file.find_int_handle_block_index(pk_val)?.unwrap();
        pks_block_idx.push(idx);
        // This should trigger handle block loading and caching
        let _result = dedicated_file.has_newer_version_int(pk_val, 50, 200)?;
    }
    assert_eq!(dedicated_file.handle_block_cache_size(), 10);

    let _result = dedicated_file.has_newer_version_int(9000, 50, 200)?;
    let idx = dedicated_file.find_int_handle_block_index(9000)?.unwrap();

    assert_eq!(dedicated_file.handle_block_cache_size(), 10);
    assert!(pks_block_idx[9] != idx);

    {
        let cache = dedicated_file.0.handle_block_cache.read().unwrap();
        let res = cache.get(&pks_block_idx[0]);
        assert!(res.is_none());
    }

    // Test cache clearing
    dedicated_file.clear_handle_block_cache();
    assert_eq!(
        dedicated_file.handle_block_cache_size(),
        0,
        "Cache should be empty after clearing"
    );

    Ok(())
}
