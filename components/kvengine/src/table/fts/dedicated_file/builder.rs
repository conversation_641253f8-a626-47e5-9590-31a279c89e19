// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::{
    collections::{BTreeMap, HashSet},
    convert::TryFrom,
    io::Write,
};

use anyhow::{anyhow, bail, Result};
use bytes::BufMut;
use hexhex::hex;
use kvenginepb::fts as ftspb;
use protobuf::Message;
use xorf::BinaryFuse8;

use super::DedicatedFileFooter;
use crate::{codecutil::next_aligned_offset, table::ChecksumType};

/// Options for building a `DedicatedFile`.
pub struct DedicatedFileBuilderOptions {
    /// Target size for Handle Blocks. Blocks may be larger if a single primary
    /// key's data causes the block to exceed this size. Default is 1 MiB.
    pub handle_block_size: usize,
    /// Checksum algorithm to use.
    pub checksum_type: ChecksumType,
}

impl Default for DedicatedFileBuilderOptions {
    fn default() -> Self {
        Self {
            handle_block_size: 1 * 1024 * 1024, // 1 MiB
            checksum_type: ChecksumType::Crc32c,
        }
    }
}

/// Internal state for building a single Handle Block.
/// This struct organizes state and can be reused via the `clear` method.
#[derive(Default)]
struct CurrentHandleBlock {
    // First PK in the block, used for writing to the index block
    start_key: Vec<u8>,
    // Current size of data written to the block
    current_size: usize,

    // Flattened buffers for efficient PK data storage
    pks_int: Vec<i64>,
    pk_common_data: Vec<u8>,
    // Contains n_pk + 1 elements. For an empty Handle Block, it always contains one element 0.
    // Each time a new PK is added, its end offset is appended.
    pk_common_offsets: Vec<u64>,

    // Other metadata for each PK
    versions: Vec<u64>,
    delete_marks: Vec<u8>,
    pk_count: u32,

    // State for checking order
    last_pk_int: i64,
    last_pk_common: Vec<u8>,
    last_version: u64,

    // Buffer for serializing the current Handle Block.
    serialized_buf: Vec<u8>,
}

impl CurrentHandleBlock {
    /// Reset state for a new Handle Block.
    fn clear(&mut self) {
        self.start_key.clear();
        self.current_size = 0;
        self.pks_int.clear();
        self.pk_common_data.clear();
        self.pk_common_offsets.clear();
        self.versions.clear();
        self.delete_marks.clear();
        self.pk_count = 0;
        self.last_pk_int = 0;
        self.last_pk_common.clear(); // Reuse buffer instead of setting to None
        self.last_version = 0;
        self.serialized_buf.clear();
    }
}

trait WrittenSize {
    /// Returns the size of data written so far.
    fn written_size(&self) -> usize;
}

impl WrittenSize for Vec<u8> {
    fn written_size(&self) -> usize {
        self.len()
    }
}

/// Builder for creating FTS DedicatedFile.
///
/// Users are responsible for providing data in the correct order:
/// 1. Primary keys (PK) must be added in ascending order.
/// 2. For a given PK, versions must be added in descending order.
///
/// The builder checks this order and returns an error if violated.
/// It buffers at most one Handle Block's worth of data to minimize memory
/// usage.
pub struct DedicatedFileBuilder<W: Write> {
    writer: W,
    options: DedicatedFileBuilderOptions,
    is_int_handle: bool,

    // Global file state
    offset: u64,
    footer: DedicatedFileFooter,
    handle_index_block: ftspb::DedicatedFileHandleIndexBlock,
    pk_hashes: HashSet<u64>, // For building the final PK filter (deduplicated)
    props: ftspb::DedicatedFilePropertyBlock,

    // State of the currently building Handle Block
    this_handle_block: CurrentHandleBlock,
}

impl<W: Write> DedicatedFileBuilder<W> {
    /// Create a new `DedicatedFileBuilder`.
    pub fn new(
        writer: W,
        options: DedicatedFileBuilderOptions,
        is_int_handle: bool,
        lp_key: &[u8],
    ) -> Result<Self> {
        // Check if LP key is empty as soon as it's provided
        if lp_key.is_empty() {
            bail!("Logical partition key cannot be empty");
        }

        let mut footer = DedicatedFileFooter::new();
        footer.checksum_type = options.checksum_type;

        let mut props = ftspb::DedicatedFilePropertyBlock::new();
        props.set_is_int_handle(is_int_handle);
        props.set_lp_key(lp_key.to_vec());

        Ok(Self {
            writer,
            options,
            is_int_handle,
            offset: 0,
            footer,
            handle_index_block: ftspb::DedicatedFileHandleIndexBlock::new(),
            pk_hashes: HashSet::new(),
            props,
            this_handle_block: CurrentHandleBlock::default(),
        })
    }

    /// Add an integer primary key (int PK).
    pub fn add_pk_int(&mut self, pk: i64, version: u64, is_delete: bool) -> Result<()> {
        if !self.is_int_handle {
            bail!("Expected integer PK, but file is configured for common PK.");
        }

        let pk_bytes = pk.to_be_bytes();

        // Check ordering constraints
        if self.this_handle_block.pk_count > 0 {
            let last_pk_int = self.this_handle_block.last_pk_int;

            if pk < last_pk_int {
                bail!(
                    "PKs must be added in ascending order. Previous: {}, Current: {}",
                    last_pk_int,
                    pk
                );
            }

            if pk == last_pk_int && version >= self.this_handle_block.last_version {
                bail!("Versions for the same PK must be added in descending order.");
            }
        } else {
            // this is the first PK in the block, record it as start_key
            self.this_handle_block
                .start_key
                .extend_from_slice(&pk_bytes);
        }

        let block = &mut self.this_handle_block;

        // Update Handle Block internal state for integer PK
        block.pks_int.push(pk);
        block.current_size += 8;
        block.versions.push(version);
        block.delete_marks.push(is_delete as u8);
        block.current_size += 8 + 1; // version + delete_mark

        block.pk_count = block
            .pk_count
            .checked_add(1)
            .ok_or_else(|| anyhow!("Too many PKs in a single Handle Block"))?;

        // Update state for order checking
        block.last_pk_int = pk;
        block.last_version = version;

        // Collect hashes for file-level PK filter (automatically deduplicated)
        self.pk_hashes.insert(farmhash::fingerprint64(&pk_bytes));

        // If the block becomes large enough, flush it
        if block.current_size >= self.options.handle_block_size {
            self.finish_handle_block()?;
        }

        Ok(())
    }

    /// Add a common primary key (common PK).
    pub fn add_pk_common(&mut self, pk: &[u8], version: u64, is_delete: bool) -> Result<()> {
        if self.is_int_handle {
            bail!("Expected common PK, but file is configured for integer PK.");
        }
        if pk.is_empty() {
            bail!("Common PK cannot be empty.");
        }

        // Check ordering constraints
        if self.this_handle_block.pk_count > 0 {
            let last_pk_common = self.this_handle_block.last_pk_common.as_slice();
            if pk < last_pk_common {
                bail!(
                    "PKs must be added in ascending order. Previous: {}, Current: {}",
                    hex(&last_pk_common),
                    hex(pk)
                );
            }

            if pk == last_pk_common && version >= self.this_handle_block.last_version {
                bail!("Versions for the same PK must be added in descending order.");
            }
        } else {
            // this is the first PK in the block, record it as start_key
            self.this_handle_block.start_key.extend_from_slice(pk);
            self.this_handle_block.pk_common_offsets.push(0); // First offset is always 0
        }

        let block = &mut self.this_handle_block;

        // Update Handle Block internal state for common PK
        block.pk_common_data.extend_from_slice(pk);
        block
            .pk_common_offsets
            .push(u64::try_from(block.pk_common_data.len())?);
        block.current_size += pk.len() + 8; // pk data + one u64 offset
        block.versions.push(version);
        block.delete_marks.push(is_delete as u8);
        block.current_size += 8 + 1; // version + delete_mark

        block.pk_count = block
            .pk_count
            .checked_add(1)
            .ok_or_else(|| anyhow!("Too many PKs in a single Handle Block"))?;

        // Update state for order checking - reuse buffer instead of allocating new Vec
        block.last_pk_common.clear();
        block.last_pk_common.extend_from_slice(pk);
        block.last_version = version;

        // Collect hashes for file-level PK filter (automatically deduplicated)
        self.pk_hashes.insert(farmhash::fingerprint64(pk));

        // If the block becomes large enough, flush it
        if block.current_size >= self.options.handle_block_size {
            self.finish_handle_block()?;
        }

        Ok(())
    }

    /// Serialize and write the current Handle Block.
    /// Handle Block structure:
    /// u32    Number of Primary Keys
    /// ...    Align to 8 bytes
    /// [u64]  Versions (mmap mapped)
    /// [u8]   Delete Mark (mmap mapped)
    /// ...    Align to 8 bytes
    /// [u64]  Common Handle Offsets (only exists for Common Handle, mmap
    /// mapped, has n+1 items) ...    Align to 8 bytes
    /// ...    Primary Key Data Bytes (mmap mapped)
    /// u32    Checksum (checksum of all above fields)
    /// ...    Align to 8 bytes
    fn finish_handle_block(&mut self) -> Result<()> {
        if self.this_handle_block.pk_count == 0 {
            return Ok(()); // No data, no need to write
        }

        let block = &mut self.this_handle_block;
        let buf = &mut block.serialized_buf;
        buf.clear();

        // 1. PK count (u32)
        buf.put_u32_le(block.pk_count);

        // 2. Align to 8 bytes
        Self::write_align_padding(buf, 8);

        // 3. Versions array [u64] (mmap mapped)
        buf.put_slice(bytemuck::cast_slice(&block.versions));

        // 4. Delete marks array [u8] (mmap mapped)
        buf.put_slice(&block.delete_marks);

        // 5. Align to 8 bytes
        Self::write_align_padding(buf, 8);

        // 6. Common PK offsets array [u64] (only exists for Common Handle, mmap mapped,
        //    has n+1 items)
        if !self.is_int_handle {
            buf.put_slice(bytemuck::cast_slice(&block.pk_common_offsets));

            // 7. Align to 8 bytes
            Self::write_align_padding(buf, 8);
        }

        // 8. PK data bytes (mmap mapped)
        if self.is_int_handle {
            buf.put_slice(bytemuck::cast_slice(&block.pks_int));
        } else {
            buf.put_slice(&block.pk_common_data);
        }

        // 9. Checksum (u32) - checksum of all above fields
        let checksum = self.options.checksum_type.checksum(buf);
        buf.put_u32_le(checksum);

        // 10. Align to 8 bytes
        Self::write_align_padding(buf, 8);

        // --- Write Handle Block to writer ---
        self.handle_index_block
            .handle_block_start_key
            .push(block.start_key.clone());
        self.handle_index_block
            .handle_block_offsets
            .push(self.offset);

        self.writer.write_all(buf)?;
        self.offset += buf.len() as u64;

        // Clear current block state for the next block
        block.clear();

        Ok(())
    }

    /// Complete the build process, writing all remaining buffered data,
    /// metadata blocks, and footer. Returns the total file size.
    pub fn finish(mut self, tantivy_files: BTreeMap<String, Vec<u8>>) -> Result<u64> {
        // Check if there's at least one PK
        if self.pk_hashes.is_empty() {
            bail!("At least one primary key is required to build DedicatedFile");
        }

        // Flush the last Handle Block (if it has data)
        self.finish_handle_block()?;

        // Add the final offset to handle_block_offsets to ensure N+1 length
        // This represents the end of the last handle block
        self.handle_index_block
            .handle_block_offsets
            .push(self.offset);

        // Note: handle_block_offsets should have N+1 elements where N is the number of
        // handle blocks This allows for proper range calculation: [start_i,
        // start_{i+1}) for each block

        // --- Write Data Block (containing all Tantivy files) ---
        let mut tantivy_props = ftspb::DedicatedFileTantivyFiles::new();
        let data_block_start_offset = self.offset;

        // Set data block offset in footer
        self.footer.data_block_offset = data_block_start_offset;

        // Ensure files are written in fixed order for reading
        let file_order = [
            "json0", // meta.json
            "json1", // .managed.json
            "term",
            "idx",
            "pos",
            "store",
            "fast",
            "fieldnorm",
        ];
        for name in file_order {
            if let Some(data) = tantivy_files.get(name) {
                let mut range = ftspb::DedicatedFileFileRange::new();
                range.set_offset(self.offset - data_block_start_offset);
                range.set_size(data.len() as u64);

                match name {
                    "json0" => tantivy_props.set_meta(range),
                    "json1" => tantivy_props.set_managed(range),
                    "term" => tantivy_props.set_term(range),
                    "idx" => tantivy_props.set_idx(range),
                    "pos" => tantivy_props.set_pos(range),
                    "store" => tantivy_props.set_store(range),
                    "fast" => tantivy_props.set_fast(range),
                    "fieldnorm" => tantivy_props.set_fieldnorm(range),
                    _ => bail!("match not exist file in tantivy meta files"),
                }

                self.writer.write_all(data)?;
                self.offset += data.len() as u64;

                // Align each file for mmap safety
                let padding = [0u8; 8];
                let pad_len = next_aligned_offset(self.offset as usize, 8) - (self.offset as usize);
                self.writer.write_all(&padding[..pad_len])?;
                self.offset += pad_len as u64;
            }
        }
        self.props.set_tantivy_files(tantivy_props);

        // --- Write metadata blocks ---
        let mut meta_checksum = 0;

        // Write Handle Index Block
        self.footer.handle_index_block_offset = self.offset;
        let index_data = self.handle_index_block.write_to_bytes()?;
        self.writer.write_all(&index_data)?;
        self.offset += index_data.len() as u64;
        meta_checksum = self
            .options
            .checksum_type
            .append(meta_checksum, &index_data);

        // Write PK Filter Block
        self.footer.pk_filter_block_offset = self.offset;
        let pk_hashes_vec: Vec<u64> = self.pk_hashes.iter().copied().collect();
        let pk_filter = BinaryFuse8::try_from(&pk_hashes_vec)
            .map_err(|e| anyhow!("Failed to build PK filter: {}", e))?;
        let filter_data = pk_filter.to_vec();
        self.writer.write_all(&filter_data)?;
        self.offset += filter_data.len() as u64;
        meta_checksum = self
            .options
            .checksum_type
            .append(meta_checksum, &filter_data);

        // Write Property Block
        self.footer.prop_offset = self.offset;
        let props_data = self.props.write_to_bytes()?;
        self.writer.write_all(&props_data)?;
        self.offset += props_data.len() as u64;
        meta_checksum = self
            .options
            .checksum_type
            .append(meta_checksum, &props_data);

        // --- Write Footer ---
        self.footer.checksum_other_meta = meta_checksum;
        let n = self.footer.marshal(&mut self.writer)?;
        self.offset += n as u64;

        Ok(self.offset)
    }

    /// Append zero padding to buffer to meet alignment requirements.
    fn write_align_padding<B: BufMut + WrittenSize>(buf: &mut B, align: usize) {
        let written = buf.written_size();
        let next_offset = next_aligned_offset(written, align);
        if next_offset > written {
            buf.put_bytes(0, next_offset - written);
        }
    }
}

#[cfg(test)]
mod tests {
    use std::collections::BTreeMap;

    use anyhow::Result;

    use super::*;
    use crate::table::ChecksumType;

    /// Create a test DedicatedFileBuilder
    fn create_test_builder(is_int_handle: bool) -> DedicatedFileBuilder<Vec<u8>> {
        let lp_key = "test_lp";
        let writer = Vec::new();
        let options = DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024, // 1 MiB
            checksum_type: ChecksumType::Crc32,
        };
        DedicatedFileBuilder::new(writer, options, is_int_handle, lp_key.as_bytes()).unwrap()
    }

    /// Finish building and return result size
    fn finish_builder(builder: DedicatedFileBuilder<Vec<u8>>) -> Result<u64> {
        let tantivy_files = BTreeMap::new(); // Empty Tantivy files
        builder.finish(tantivy_files)
    }

    // ==================== Error handling tests ====================

    #[test]
    fn test_builder_error_int_pk_type_mismatch() {
        let mut builder = create_test_builder(true); // Integer PK mode

        // Trying to add common PK should fail
        let result = builder.add_pk_common(b"common_pk", 100, false);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("Expected common PK, but file is configured for integer PK")
        );
    }

    #[test]
    fn test_builder_error_common_pk_type_mismatch() {
        let mut builder = create_test_builder(false); // Common PK mode

        // Trying to add integer PK should fail
        let result = builder.add_pk_int(123, 100, false);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("Expected integer PK, but file is configured for common PK")
        );
    }

    #[test]
    fn test_builder_error_empty_common_pk() {
        let mut builder = create_test_builder(false);

        // Trying to add empty common PK should fail
        let result = builder.add_pk_common(b"", 100, false);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("Common PK cannot be empty")
        );
    }

    #[test]
    fn test_builder_add_int_pk() {
        let mut builder = create_test_builder(true);

        let result = builder.add_pk_int(-100, 100, false);
        assert!(result.is_ok());

        let result = builder.add_pk_int(100, 100, false);
        assert!(result.is_ok());
    }

    #[test]
    fn test_builder_error_pk_order_int() {
        let mut builder = create_test_builder(true);

        // Add first PK
        builder.add_pk_int(200, 100, false).unwrap();

        // Trying to add smaller PK should fail
        let result = builder.add_pk_int(100, 99, false);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("PKs must be added in ascending order")
        );
    }

    #[test]
    fn test_builder_error_pk_order_common() {
        let mut builder = create_test_builder(false);

        // Add first PK (larger in byte order)
        builder.add_pk_common(b"zebra", 100, false).unwrap();

        // Trying to add smaller PK in byte order should fail
        let result = builder.add_pk_common(b"apple", 99, false);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("PKs must be added in ascending order")
        );
    }

    #[test]
    fn test_builder_error_version_order() {
        let mut builder = create_test_builder(true);

        // Add first version
        builder.add_pk_int(100, 200, false).unwrap();

        // Trying to add higher version for same PK should fail
        let result = builder.add_pk_int(100, 300, false);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("Versions for the same PK must be added in descending order")
        );
    }

    #[test]
    fn test_builder_error_finish_without_pks() {
        let builder = create_test_builder(true);

        // Trying to finish build without adding any PKs should fail
        let result = finish_builder(builder);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("At least one primary key is required to build DedicatedFile")
        );
    }

    #[test]
    fn test_builder_error_empty_lp_key() {
        let lp_key = b""; // Empty LP key
        let writer = Vec::new();
        let options = DedicatedFileBuilderOptions {
            handle_block_size: 1024 * 1024,
            checksum_type: ChecksumType::Crc32,
        };
        let res = DedicatedFileBuilder::new(writer, options, true, lp_key);
        assert!(res.is_err());

        if let Err(err) = res {
            assert!(
                err.to_string()
                    .contains("Logical partition key cannot be empty")
            );
        }
    }
}
