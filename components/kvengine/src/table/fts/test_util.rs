// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use crate::table::fts::{
    DedicatedFileFooter, PackedFileFooter, FTS_DEDICATED_FILE_FOOTER_SIZE,
    FTS_PACKED_FILE_FOOTER_SIZE,
};

pub fn get_packed_file_metadata_offset(file_content: &[u8]) -> u64 {
    assert!(file_content.len() >= FTS_PACKED_FILE_FOOTER_SIZE);
    let footer = PackedFileFooter::unmarshal(
        &file_content[file_content.len() - FTS_PACKED_FILE_FOOTER_SIZE..],
    )
    .unwrap();
    footer.metadata_offset()
}

pub fn get_dedicated_file_meta_offset(file_content: &[u8]) -> u64 {
    assert!(file_content.len() >= FTS_DEDICATED_FILE_FOOTER_SIZE);
    let footer = DedicatedFileFooter::unmarshal(
        &file_content[file_content.len() - FTS_DEDICATED_FILE_FOOTER_SIZE..],
    )
    .unwrap();

    // Returns the offset of the first metadata block (Handle Index Block).
    footer.handle_index_block_offset
}
