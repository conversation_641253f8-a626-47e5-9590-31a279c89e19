// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::{
    fmt::Debug,
    sync::{Arc, RwLock},
};

use anyhow::{anyhow, bail, Result};
use bytes::{Buf, Bytes};
use hexhex::hex;
use kvenginepb::fts as ftspb;
use protobuf::Message;
use xorf::{BinaryFuse8, Filter};

use crate::{
    codecutil::{next_aligned_offset, BytesExt},
    ia::ia_file::{IaFile, IaMmapSource},
    table::{
        file::{File, TtlCache},
        ChecksumType,
    },
};

mod builder;

#[cfg(test)]
mod test;

pub use builder::*;

/// Magic number for FTS PackedFile format
pub const FTS_PACKED_FILE_MAGIC: u32 = 0xFE75F11E;

/// Format version for FTS PackedFile
pub const FTS_PACKED_FILE_FORMAT_V1: u8 = 0x1;

/// Size of the serialized footer in bytes
pub const FTS_PACKED_FILE_FOOTER_SIZE: usize = 32;

/// Footer has a fixed size and is always located at the end of the file.
/// Footer size cannot be changed.
/// Format:
/// - 1 byte: format version
/// - 1 byte: checksum type
/// - 6 bytes: reserved
/// - 4 bytes: checksum footer
///            > Calculated as the checksum of the whole footer (with checksum
///            > footer set to 0)
/// - 4 bytes: checksum (remaining meta)
/// - 4 bytes: index block offset
/// - 4 bytes: lp (LogicalPartition) filter block offset
/// - 4 bytes: property block offset
/// - 4 bytes: magic number
#[repr(C)]
#[derive(Default, Clone, Copy, Debug)]
pub struct PackedFileFooter {
    pub format: u8,
    pub checksum_type: ChecksumType,
    pub checksum_other_meta: u32,
    pub index_block_offset: u32,
    pub lp_filter_block_offset: u32,
    pub prop_offset: u32,
    pub magic: u32,
}

impl PackedFileFooter {
    /// Get the size of the footer
    pub const fn footer_size() -> usize {
        FTS_PACKED_FILE_FOOTER_SIZE
    }

    /// Create a new footer with default values
    pub fn new() -> Self {
        Self {
            format: FTS_PACKED_FILE_FORMAT_V1,
            checksum_type: ChecksumType::None,
            checksum_other_meta: 0,
            index_block_offset: 0,
            lp_filter_block_offset: 0,
            prop_offset: 0,
            magic: FTS_PACKED_FILE_MAGIC,
        }
    }

    /// Unmarshal footer from bytes
    pub fn unmarshal(mut data: &[u8]) -> Result<Self> {
        if data.len() != FTS_PACKED_FILE_FOOTER_SIZE {
            bail!(
                "Invalid footer size, expected {:#x}, got {:#x}",
                FTS_PACKED_FILE_FOOTER_SIZE,
                data.len()
            );
        }
        let mut copied_data = [0u8; FTS_PACKED_FILE_FOOTER_SIZE];
        copied_data.copy_from_slice(data);

        let format = data.get_u8();
        let checksum_type_value = data.get_u8();
        let checksum_type = ChecksumType::from(checksum_type_value);
        data.advance(6);
        let checksum_footer = data.get_u32_le();
        let checksum_other_meta = data.get_u32_le();
        let index_block_offset = data.get_u32_le();
        let lp_filter_block_offset = data.get_u32_le();
        let prop_offset = data.get_u32_le();
        let magic = data.get_u32_le();
        if magic != FTS_PACKED_FILE_MAGIC {
            bail!(
                "Invalid magic number, expected {:#x}, got {:#x}",
                FTS_PACKED_FILE_MAGIC,
                magic
            );
        }
        if format != FTS_PACKED_FILE_FORMAT_V1 {
            bail!(
                "Unsupported format version, expected {:#x}, got {:#x}",
                FTS_PACKED_FILE_FORMAT_V1,
                format
            );
        }

        {
            // Verify footer's checksum
            copied_data[8..12].fill(0);
            let checksum_footer_actual = checksum_type.checksum(&copied_data);
            if checksum_footer != checksum_footer_actual {
                copied_data[8..12].copy_from_slice(&checksum_footer.to_le_bytes());
                bail!(
                    "FtsPackedFile footer checksum mismatch, actual {:#08x}, expect {:#08x}, footer data: {}, ChecksumType={:?}",
                    checksum_footer_actual,
                    checksum_footer,
                    hex(&copied_data),
                    checksum_type,
                );
            }
        }

        Ok(Self {
            format,
            checksum_type,
            checksum_other_meta,
            index_block_offset,
            lp_filter_block_offset,
            prop_offset,
            magic,
        })
    }

    /// Marshal footer to bytes
    pub fn marshal<W: std::io::Write>(&self, mut w: W) -> Result<usize> {
        use bytes::BufMut;

        let mut footer = [0u8; FTS_PACKED_FILE_FOOTER_SIZE];
        let mut data = &mut footer[..];
        data.put_u8(self.format);
        data.put_u8(self.checksum_type.value());
        data.put_slice(&[0; 6]); // Reserved bytes
        data.put_slice(&[0; 4]); // Placeholder for checksum footer
        data.put_u32_le(self.checksum_other_meta);
        data.put_u32_le(self.index_block_offset);
        data.put_u32_le(self.lp_filter_block_offset);
        data.put_u32_le(self.prop_offset);
        data.put_u32_le(self.magic);

        let checksum_footer = self.checksum_type.checksum(&footer);
        footer[8..12].copy_from_slice(&checksum_footer.to_le_bytes());

        w.write_all(&footer)?;

        Ok(FTS_PACKED_FILE_FOOTER_SIZE)
    }

    /// Returns the starting offset of the metadata blocks in the PackedFile
    /// indicated by this footer.
    ///
    /// As undex Block is the first metadata block, its offset is returned.
    pub fn metadata_offset(&self) -> u64 {
        self.index_block_offset as u64
    }
}

/// A FtsPackedFile in memory. Only minimal metadata is kept in memory.
/// The main data is accessed through mmap and acceleration data is cached via
/// TtlCache.
///
/// FtsPackedFile layout:
/// - Data blocks:     Each data block contains several LP entries
///                    > Data block is aligned to 8 bytes, paddings may be added
///                    > at the *beginning*
/// - Index block:     For quickly seeking a data block
/// - LP filter block: For checking whether LPKey must not exist
/// - Property block:  Some additional properties (in Protobuf)
/// - Footer:          Metadata
#[derive(Clone)]
pub struct PackedFile<Info>(Arc<PackedFileCore<Info>>);

/// Core implementation of PackedFile
struct PackedFileCore<Info> {
    file: Arc<IaFile>,

    index_block: TtlCache<ftspb::PackedFileIndexBlock>,
    lp_filter: TtlCache<BinaryFuse8>,
    props: ftspb::PackedFilePropertyBlock,
    footer: PackedFileFooter,
    data_block_checked: RwLock<Vec<bool>>,

    additional_info: Info,
}

impl PackedFile<()> {
    /// Create a new PackedFile from a remote file.
    pub fn new(file: Arc<IaFile>) -> Result<PackedFile<()>> {
        PackedFile::<()>::new_with_info(file, ())
    }

    /// Calculate IA segment offsets for an FTS packed file based on data
    /// blocks. This function reads the index block and calculates segment
    /// boundaries based on the standard IA segment size
    pub fn generate_ia_segment_boundaries(file: &IaFile, segment_size: u64) -> Result<Vec<u64>> {
        let footer = Self::load_footer(file)?;
        let index_block = Self::load_index_block(file, &footer)?;

        let mut segment_offsets = vec![0]; // Always start with offset 0
        let mut current_segment_start = 0u64;

        // Iterate through data block offsets and create segments when accumulated size
        // reaches the standard IA segment size
        for &block_offset in &index_block.data_block_offsets {
            let block_offset = block_offset as u64;
            // If this block would make the current segment exceed the segment size,
            // start a new segment
            if block_offset >= current_segment_start + segment_size {
                segment_offsets.push(block_offset);
                current_segment_start = block_offset;
            }
        }

        Ok(segment_offsets)
    }

    /// Load the footer from the packed file.
    fn load_footer(file: &IaFile) -> Result<PackedFileFooter> {
        let footer_data = file.read_footer(FTS_PACKED_FILE_FOOTER_SIZE)?;
        let footer = PackedFileFooter::unmarshal(footer_data.as_ref())?;
        Self::verify_meta_checksum(file, &footer)?;
        Ok(footer)
    }

    /// Load the properties block from the packed file.
    fn load_props(
        file: &IaFile,
        footer: &PackedFileFooter,
    ) -> Result<ftspb::PackedFilePropertyBlock> {
        let prop_offset = footer.prop_offset as u64;
        let prop_len = file
            .size()
            .checked_sub(FTS_PACKED_FILE_FOOTER_SIZE as u64 + prop_offset)
            .ok_or_else(|| {
                anyhow!(
                    "Property block offset {} is out of bounds for file size {}",
                    prop_offset,
                    file.size()
                )
            })? as usize;
        let mut prop = ftspb::PackedFilePropertyBlock::new();
        if prop_len > 0 {
            let data = file.read_table_meta(prop_offset, prop_len)?;
            prop.merge_from_bytes(&data)?;
        }
        Ok(prop)
    }

    /// Verify the metadata checksum for the packed file.
    fn verify_meta_checksum(file: &IaFile, footer: &PackedFileFooter) -> Result<()> {
        let offset = footer.metadata_offset();
        // The size should be the metadata size minus the footer size
        // file.size() = table_meta_off + meta_size, so meta_size = file.size() -
        // table_meta_off
        let meta_size = file.size() - file.table_meta_off;
        let size = meta_size as usize - FTS_PACKED_FILE_FOOTER_SIZE;
        let rest_meta_data = file.read_table_meta(offset, size)?;
        let checksum_actual = footer.checksum_type.checksum(&rest_meta_data);
        if checksum_actual != footer.checksum_other_meta {
            bail!(
                "FtsPackedFile meta checksum mismatch, actual {:#08x}, expect {:#08x}, ChecksumType={:?}",
                checksum_actual,
                footer.checksum_other_meta,
                footer.checksum_type
            );
        }
        Ok(())
    }

    /// Load the index block from the packed file.
    fn load_index_block(
        file: &IaFile,
        footer: &PackedFileFooter,
    ) -> Result<ftspb::PackedFileIndexBlock> {
        let offset = footer.index_block_offset as u64;
        let length = footer.lp_filter_block_offset - footer.index_block_offset;
        let data = file.read_table_meta(offset, length as usize)?;

        let mut index_block = ftspb::PackedFileIndexBlock::new();
        index_block.merge_from_bytes(&data)?;

        if index_block.data_block_offsets.is_empty() {
            bail!("Index block broken: data_block_offsets must not be empty");
        }
        if index_block.data_block_start_keys.len() + 1 != index_block.data_block_offsets.len() {
            bail!(
                "Index block broken: data_block_start_keys.len={} but data_block_offsets.len={}",
                index_block.data_block_start_keys.len(),
                index_block.data_block_offsets.len()
            );
        }

        Ok(index_block)
    }

    /// Load the LP filter block from the packed file.
    fn load_lp_filter(file: &IaFile, footer: &PackedFileFooter) -> Result<BinaryFuse8> {
        let offset = footer.lp_filter_block_offset as u64;
        let length = footer.prop_offset - footer.lp_filter_block_offset;
        let data = file.read_table_meta(offset, length as usize)?;

        let filter =
            BinaryFuse8::try_from_bytes(&data).map_err(|e| anyhow!("Bad LPKey filter: {}", e))?;
        Ok(filter)
    }
}

impl<Info: Debug> Debug for PackedFile<Info> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("PackedFile")
            .field("file", &self.0.file)
            .field("props", &self.0.props)
            .field("info", &self.0.additional_info)
            .finish()
    }
}

impl<Info> PackedFile<Info> {
    pub fn new_with_info(file: Arc<IaFile>, additional_info: Info) -> Result<PackedFile<Info>> {
        if file.is_sync() {
            // Currently local file is not supported.
            bail!(
                "FtsPackedFile can be only accessed via IaFile, got {:?}",
                file.path()
            );
        }

        let footer = PackedFile::load_footer(file.as_ref())?;
        let props = PackedFile::load_props(file.as_ref(), &footer)?;
        Ok(Self(Arc::new(PackedFileCore::<_> {
            file,
            index_block: TtlCache::default(),
            lp_filter: TtlCache::default(),
            props,
            footer,
            data_block_checked: RwLock::new(vec![]),

            additional_info,
        })))
    }

    /// Get or load the index block.
    /// Index block can be used to quickly find which data block contains the
    /// given logical partition key.
    fn get_index_block(&self) -> Result<Arc<ftspb::PackedFileIndexBlock>> {
        self.0
            .index_block
            .get(|| PackedFile::load_index_block(&self.0.file, &self.0.footer))
    }

    /// Get or load the LogicalPartition filter.
    /// LogicalPartition filter can be used to quickly check whether this packed
    /// file contains a logical partition key.
    fn get_lp_filter(&self) -> Result<Arc<BinaryFuse8>> {
        self.0
            .lp_filter
            .get(|| PackedFile::load_lp_filter(&self.0.file, &self.0.footer))
    }

    /// Returns the property block.
    pub fn props(&self) -> &ftspb::PackedFilePropertyBlock {
        &self.0.props
    }

    /// Return the number of data blocks.
    pub fn n_data_blocks(&self) -> Result<usize> {
        Ok(self.get_index_block()?.data_block_start_keys.len())
    }

    /// Get a logical partition for future access.
    /// Note: This function will download the data block if it is not in IA.
    pub async fn get_logical_partition(&self, lp_key: &[u8]) -> Result<Option<PackedFileLp>> {
        let lp_filter = self.get_lp_filter()?;
        let key_hash = farmhash::fingerprint64(lp_key);
        if !lp_filter.contains(&key_hash) {
            return Ok(None);
        }

        // Navigate the data block idx from index block.
        let index_block = self.get_index_block()?;
        let data_block_idx = self.find_data_block_index(&index_block, lp_key);
        let data_block_idx = match data_block_idx {
            Some(idx) => idx,
            None => return Ok(None),
        };

        // Retrieve the data of data block.
        let data_block_offset = index_block.data_block_offsets[data_block_idx];
        let data_block_len = index_block.data_block_offsets[data_block_idx + 1] - data_block_offset;

        // TODO: Avoid repeat mmap when segment is already mapped.
        let (data, guard) = self
            .0
            .file
            .mmap_range(data_block_offset as u64, data_block_len as usize)
            .await?;

        let data_block = PackedFileDataBlockAccessor::new(data, guard)?;

        let need_checksum = {
            let data_block_checked = self.0.data_block_checked.read().unwrap();
            data_block_checked.is_empty() || !data_block_checked[data_block_idx]
        };
        if need_checksum {
            let checksum_type = ChecksumType::from(self.0.footer.checksum_type as u8);
            data_block.verify_checksum(checksum_type)?;
            {
                let mut data_block_checked = self.0.data_block_checked.write().unwrap();
                if data_block_checked.is_empty() {
                    data_block_checked.resize(index_block.data_block_offsets.len(), false);
                }
                data_block_checked[data_block_idx] = true;
            }
        }

        data_block.find_lp(lp_key)
    }

    /// Find the data block index that may contain the given logical partition
    /// key. It returns None when data block must not exist (there is no
    /// data block, or the key is smaller than the key of the first data
    /// block).
    ///
    /// If a data block is returned, you should further check whether the data
    /// block contains the given logical partition key by yourself.
    fn find_data_block_index(
        &self,
        index_block: &ftspb::PackedFileIndexBlock,
        lp_key: &[u8],
    ) -> Option<usize> {
        let n = index_block.data_block_start_keys.len();
        let pos = crate::table::search(n, |i| {
            index_block.data_block_start_keys[i].as_slice() > lp_key
        });
        // Now: [..pos), start_key <= lp_key
        //      [pos..): start_key > lp_key
        // So pos-1 is what we want.
        if pos == 0 {
            return None;
        }
        Some(pos - 1)
    }
}

/// A zero-copy accessor for reading a data block in PackedFile.
/// Creating the accessor is designed to be very cheap.
///
/// DataBlock layout:
/// ...    (Align to 8 bytes)
/// ...    Entries
///        > Each entry is a logical partition
///        > Each entry is aligned to 8 bytes
///        > Entries are ordered by LPKey
/// ...    (Align to 8 bytes)
/// [u32]  Entry Offsets (n=Entries Count)
/// u32    (Additional) Last Entry End Offset
/// u32    Entries Count
/// u32    Checksum (start from 0 byte including paddings)
struct PackedFileDataBlockAccessor {
    data_block: Bytes,
    source_guard: IaMmapSource,

    /// It contains n_entries+1 elements, where the last element is the end
    /// offset.
    ///
    /// 'static because comes from `entries_data`.
    /// MUST NOT expose to outside.
    offsets: &'static [u32],
    n_entries: u32,
    checksum: u32,
}

impl PackedFileDataBlockAccessor {
    fn new(mut data_block: Bytes, source_guard: IaMmapSource) -> Result<Self> {
        let data_block_clone = data_block.clone();
        let checksum = data_block.try_get_last_u32_le()?;
        let n_entries = data_block.try_get_last_u32_le()?;
        let offsets_data = data_block.try_get_last((n_entries + 1) as usize * 4)?;
        let offsets = offsets_data.try_as_slice::<u32>()?;
        Ok(PackedFileDataBlockAccessor {
            data_block: data_block_clone,
            source_guard,

            offsets: unsafe { extend_lifetime(offsets) },
            n_entries,
            checksum,
        })
    }

    fn verify_checksum(&self, checksum_type: ChecksumType) -> Result<()> {
        let content = &self.data_block.as_ref()[..self.data_block.len() - 4];
        let checksum_actual = checksum_type.checksum(content);
        if self.checksum != checksum_actual {
            bail!(
                "FtsPackedFile data block checksum mismatch, actual {:#08x}, expect {:#08x}, ChecksumType={:?}",
                checksum_actual,
                self.checksum,
                checksum_type
            );
        }
        Ok(())
    }

    fn entry_data_at(&self, i: usize) -> Result<Bytes> {
        let offset = self.offsets[i] as usize;
        let end_offset = self.offsets[i + 1] as usize;
        let entry_data = self.data_block.try_slice(offset..end_offset)?;
        entry_data.check_aligned::<u64>()?;
        Ok(entry_data)
    }

    /// Extracts the LPKey from the entry data without constructing
    /// PackedFileLp.
    fn fast_extract_lp_key(entry_data: &Bytes) -> Result<Bytes> {
        let mut entry_data = entry_data.clone();
        let key_len = entry_data.try_get_u16_le()?;
        if key_len == 0 {
            bail!("Broken entry data, unexpected empty key length");
        }
        entry_data.try_get_first(key_len as usize)
    }

    fn find_lp(&self, lp_key: &[u8]) -> Result<Option<PackedFileLp>> {
        let n_entries = self.n_entries as usize;
        let pos = crate::table::try_search(n_entries, |i| {
            let entry_data = self.entry_data_at(i)?;
            Self::fast_extract_lp_key(&entry_data).map(|key| key.as_ref() >= lp_key)
        })?;
        if pos == n_entries {
            return Ok(None);
        }
        let entry_data = self.entry_data_at(pos)?;
        let lp_key_at_pos = Self::fast_extract_lp_key(&entry_data)?;
        if lp_key_at_pos.as_ref() != lp_key {
            return Ok(None);
        }
        Ok(Some(PackedFileLp::new(
            entry_data,
            self.source_guard.clone(),
        )?))
    }
}

/// Reading from a logical partition in PackedFile.
/// This struct is supposed to be cached.
///
/// Entry data layout:
/// u16    LogicalPartitionKey Length
/// ...    LogicalPartitionKey Bytes
/// u32    Number of Primary Keys (n_pk)
///        > Primary keys may duplicate, means there are multiple versions.
/// u8     Flag - Primary Key is Int Handle (is_pk_int)
/// ...    (Align to 8 bytes)
/// [u64]  Versions (For the same PK, they are ordered from largest to smallest)
/// [u8]   Delete Marks
/// ...    (Align to 4 bytes)
/// [u32]  Common Handle Offsets (only for Common Handle), n=n_pk+1
/// ...    (Align to 8 bytes)
/// ...    Primary Key Data Bytes (ordered by PK in ascending order)
/// u32    Primary Key Filter Length
/// ...    Primary Key Filter Bytes
/// u32    Tantivy Index Data Length
/// ...    Tantivy Index Data Bytes
/// ...    (Align to 8 bytes)
///
/// For example, we may have such data (unordered):
/// (PK, Version)
/// (7,  2)
/// (1,  1)
/// (1,  3)
/// (3,  100)
///
/// They will be stored in PackedFileLp in this way:
/// PK:      [1, 1, 3,   7] (ascending order)
/// Version: [3, 1, 100, 2] (newer version comes first for the same PK)
#[derive(Clone)]
pub struct PackedFileLp(Arc<PackedFileLpCore>);

struct PackedFileLpCore {
    _entry_data: Bytes, /* Contains the whole entry data. It must be kept so that the rest
                         * fields' lifetime is valid */

    #[allow(unused)]
    source_guard: IaMmapSource,

    lp_key: Bytes,

    n_pk: u32,
    is_pk_int: bool,

    /// 'static because comes from `entry_data`.
    /// MUST NOT expose to outside.
    versions: &'static [u64],
    /// 'static because comes from `entry_data`.
    /// MUST NOT expose to outside.
    #[allow(unused)]
    delete_marks: &'static [u8],

    /// Only exists when pk is common PK (is_pk_int == false).
    /// It contains n_pk+1 elements, where the last element is the end offset.
    /// 'static because comes from `entry_data`.
    /// MUST NOT expose to outside.
    pk_common_offsets: &'static [u32],

    /// for Int PK, size=n_pk*8, must be aligned by 8 bytes
    /// for Common PK, size varies.
    pk_data: Bytes,

    /// Only exists when pk is int PK (is_pk_int == true).
    /// 'static because comes from `entry_data`.
    /// MUST NOT expose to outside.
    pk_data_int: &'static [i64],

    pk_filter: BinaryFuse8, // The underlying storage is still entry_data, sharing same lifecycle.

    #[allow(unused)]
    tantivy_index_data: Bytes,
    // tantivy_index_cache: TtlCache<clara_fts::IndexReader>,
}

impl PackedFileLp {
    /// Creates a new PackedFileEntryDataAccessor from the given entry data.
    /// PackedFileEntryDataAccessor is too large so it is wrapped in a Box.
    fn new(mut entry_data: Bytes, source_guard: IaMmapSource) -> Result<PackedFileLp> {
        entry_data.check_aligned::<u64>()?;

        let mut current_offset = 0usize;

        // Read LogicalPartitionKey Length and skip key bytes
        let lp_key_len = entry_data.try_get_u16_le()? as usize;
        current_offset += 2;
        let lp_key = entry_data.try_get_first(lp_key_len)?;
        current_offset += lp_key_len;

        // Read Number of Primary Keys
        let n_pk = entry_data.try_get_u32_le()?;
        current_offset += 4;

        // Read Flag - Primary Key is Int Handle
        let is_pk_int = entry_data.try_get_u8()? != 0;
        current_offset += 1;

        // Align to 8 bytes
        let next_offset = next_aligned_offset(current_offset, 8);
        if next_offset > current_offset {
            entry_data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // Read Versions (n_pk * 8 bytes)
        let versions_data = entry_data.try_get_first((n_pk as usize) * 8)?;
        let versions = versions_data.try_as_slice::<u64>()?;
        current_offset += versions_data.len();

        // Read Delete Marks (n_pk bytes)
        let delete_marks_data = entry_data.try_get_first(n_pk as usize)?;
        let delete_marks = delete_marks_data.try_as_slice::<u8>()?;
        current_offset += delete_marks_data.len();

        // Align to 4 bytes
        let next_offset = next_aligned_offset(current_offset, 4);
        if next_offset > current_offset {
            entry_data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // Read Common Handle Offsets (only for Common Handle)
        let offsets_data = if !is_pk_int {
            entry_data.try_get_first(((n_pk + 1) as usize) * 4)?
        } else {
            Bytes::new() // No offsets for Int handle
        };
        current_offset += offsets_data.len();
        let pk_common_offsets = offsets_data.try_as_slice::<u32>()?;

        // Align to 8 bytes for Primary Key Data
        let next_offset = next_aligned_offset(current_offset, 8);
        if next_offset > current_offset {
            entry_data.advance(next_offset - current_offset);
            current_offset = next_offset;
        }

        // Read Primary Key Data
        let pk_data_size = if is_pk_int {
            // For Int handle: n_pk * 8 bytes
            (n_pk as usize) * 8
        } else {
            // For Common handle
            pk_common_offsets.last().copied().unwrap() as usize
        };

        let pk_data = entry_data.try_get_first(pk_data_size)?;
        pk_data.check_aligned::<i64>()?;
        current_offset += pk_data_size;

        let pk_data_clone = pk_data.clone();
        let pk_data_int = if is_pk_int {
            // For Int handle, we can read it as i64
            pk_data_clone.try_as_slice::<i64>()?
        } else {
            // For Common handle, we do not use this field
            &[]
        };

        // Read Primary Key Filter Length and Filter Bytes
        let pk_filter_len = entry_data.try_get_u32_le()? as usize;
        let pk_filter_data = entry_data.try_get_first(pk_filter_len)?;
        current_offset += pk_filter_len;

        let pk_filter = BinaryFuse8::try_from_bytes(&pk_filter_data)
            .map_err(|e| anyhow!("Bad PK filter: {}", e))?;

        // Read Tantivy Index Data Length and Index Data Bytes
        let tantivy_len = entry_data.try_get_u32_le()? as usize;
        current_offset += 4;
        let tantivy_index_data = entry_data.try_get_first(tantivy_len)?;

        // Make lint happy
        _ = current_offset;

        Ok(PackedFileLp(Arc::new(PackedFileLpCore {
            _entry_data: entry_data,
            source_guard,
            lp_key,
            n_pk,
            is_pk_int,
            versions: unsafe { extend_lifetime(versions) },
            delete_marks: unsafe { extend_lifetime(delete_marks) },
            pk_common_offsets: unsafe { extend_lifetime(pk_common_offsets) },
            pk_data,
            pk_data_int: unsafe { extend_lifetime(pk_data_int) },
            pk_filter,
            tantivy_index_data,
        })))
    }

    /// Returns the Logical Partition Key of this LogicalPartition.
    pub fn lp_key(&self) -> &[u8] {
        &self.0.lp_key
    }

    /// Returns the number of primary keys (includes multi-version).
    pub fn n_keys(&self) -> usize {
        self.0.n_pk as usize
    }

    /// Returns whether the primary key is int.
    pub fn is_pk_int(&self) -> bool {
        self.0.is_pk_int
    }

    /// Returns the approximate memory size of this LP.
    /// Usually the approximate size is used for cache weight.
    pub fn memory_size(&self) -> usize {
        // Most data is mmapped so that they do not count towards the weight.
        std::mem::size_of::<PackedFileLpCore>()
    }

    /// Check if this file stores the specified PK with its version > given
    /// version and its version <= max_version. This is used for MVCC check.
    pub fn has_newer_version_int(&self, pk: i64, version: u64, max_version: u64) -> Result<bool> {
        if !self.is_pk_int() {
            bail!("Logical partition is common PK");
        }
        if !self.0.pk_filter.contains(&(pk as u64)) {
            return Ok(false);
        }
        has_newer_version(
            pk,
            version,
            max_version,
            self.0.n_pk as usize,
            |i| Ok(self.0.versions[i]),
            |i| Ok(self.0.pk_data_int[i]),
        )
    }

    /// Check if this file stores the specified PK with its version > given
    /// version and its version <= max_version. This is used for MVCC check.
    pub fn has_newer_version_common(
        &self,
        pk: &[u8],
        version: u64,
        max_version: u64,
    ) -> Result<bool> {
        if self.is_pk_int() {
            bail!("Logical partition is int PK");
        }
        let key_hash = farmhash::fingerprint64(pk);
        if !self.0.pk_filter.contains(&key_hash) {
            return Ok(false);
        }
        has_newer_version(
            pk,
            version,
            max_version,
            self.0.n_pk as usize,
            |i| Ok(self.0.versions[i]),
            |i| {
                let start_offset = self.0.pk_common_offsets[i] as usize;
                let end_offset = self.0.pk_common_offsets[i + 1] as usize;
                self.0
                    .pk_data
                    .as_ref()
                    .get(start_offset..end_offset)
                    .ok_or_else(|| {
                        anyhow!(
                            "PK data out of bounds: start={}, end={}, pk_data_len={}",
                            start_offset,
                            end_offset,
                            self.0.pk_data.len()
                        )
                    })
            },
        )
    }
}

/// Find whether there is a newer version of a specified PK.
///
/// We suppose PKs and versions are ordered in the following way and accessed
/// through a xxx_at() fn:
///
/// N:       4
/// PK:      [1, 1, 3,   7] (ascending order)
/// Version: [3, 1, 100, 2] (newer version comes first for the same PK)
///
/// Internally, we treat it as a globally sorted sequence of (PK, !version)
/// tuples: (1, !3), (1, !1), (3, !100), (7, !2)
/// and then we perform a binary search to find the first position
/// where (PK, !version) >= (target_pk, !target_max_version).
///
/// Returns true, if the `target_pk` is found and it has a version that is newer
/// than `target_version` and not newer than `target_max_version`.
fn has_newer_version<PK: Ord>(
    target_pk: PK,
    target_version: u64,
    target_max_version: u64,

    n: usize,
    version_at: impl Fn(usize) -> Result<u64>,
    pk_at: impl Fn(usize) -> Result<PK>,
) -> Result<bool> {
    use std::cmp::Ordering;
    // Find the first position `pos` such that:
    // `pk_at(pos) >= target_pk` AND `version_at(pos) <= target_max_version`.
    let pos = crate::table::try_search::<anyhow::Error>(n, |i| match pk_at(i)?.cmp(&target_pk) {
        Ordering::Less => Ok(false),
        Ordering::Greater => Ok(true),
        Ordering::Equal => Ok(version_at(i)? <= target_max_version),
    })?;
    if pos >= n {
        return Ok(false);
    }

    let pk = pk_at(pos)?;
    if pk == target_pk {
        let version = version_at(pos)?;
        // We already know version <= target_max_version from the search.
        // We just need to check if version > target_version.
        if version > target_version {
            return Ok(true);
        }
    }

    Ok(false)
}

unsafe fn extend_lifetime<'b, T: ?Sized>(r: &'b T) -> &'static T {
    std::mem::transmute::<&'b T, &'static T>(r)
}
