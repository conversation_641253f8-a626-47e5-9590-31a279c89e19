use std::io::Cursor;

use super::*;
use crate::ia::manager::tests::IaFileTester;

#[cfg(test)]
impl PackedFile<()> {
    #[cfg(test)]
    pub async fn from_buffer(buf: &[u8]) -> Result<(IaFileTester, PackedFile<()>)> {
        let tester = IaFileTester::default();
        let ia_file = tester
            .new_ia_file(
                crate::dfs::FileType::FtsPackedFile,
                buf,
                crate::table::fts::test_util::get_packed_file_metadata_offset(buf),
            )
            .await;
        let file = Arc::new(ia_file);
        Ok((tester, PackedFile::new(file)?))
        // tester must be placed as left so that it can be dropped after
        // packed_file
    }
}

#[test]
fn footer_marshal_unmarshal_with_checksum() {
    let mut f = PackedFileFooter::new();
    f.checksum_type = ChecksumType::Crc32c;
    f.checksum_other_meta = 0xA1B2_C3D4;
    f.index_block_offset = 0x10_20_30_40;
    f.lp_filter_block_offset = 0x20_30_40_50;
    f.prop_offset = 0x30_40_50_60;

    let mut buf = Vec::new();
    f.marshal(&mut buf).unwrap();

    // Roundtrip
    let f2 = PackedFileFooter::unmarshal(&buf).unwrap();
    assert_eq!(f2.format, f.format);
    assert_eq!(f2.checksum_type.value(), f.checksum_type.value());
    assert_eq!(f2.checksum_other_meta, f.checksum_other_meta);
    assert_eq!(f2.index_block_offset, f.index_block_offset);
    assert_eq!(f2.lp_filter_block_offset, f.lp_filter_block_offset);
    assert_eq!(f2.prop_offset, f.prop_offset);
    assert_eq!(f2.magic, f.magic);

    // Modify some byte should cause checksum mismatch
    let mut buf2 = buf.clone();
    buf2[8] ^= 0xFF; // Flip a bit in checksum field
    PackedFileFooter::unmarshal(&buf2).unwrap_err();

    // Modify some data field should also cause checksum mismatch
    let mut buf2 = buf.clone();
    buf2[15] ^= 0xFF;
    PackedFileFooter::unmarshal(&buf2).unwrap_err();
}

#[test]
fn footer_unmarshal_invalid_size() {
    // Too short
    PackedFileFooter::unmarshal(&[]).unwrap_err();
    // Too long
    let mut v = vec![0u8; FTS_PACKED_FILE_FOOTER_SIZE + 1];
    PackedFileFooter::unmarshal(&v).unwrap_err();
    // Off by one short
    v.truncate(FTS_PACKED_FILE_FOOTER_SIZE - 1);
    PackedFileFooter::unmarshal(&v).unwrap_err();
}

#[test]
fn footer_unmarshal_invalid_magic() {
    let mut f = PackedFileFooter::new();
    f.checksum_type = ChecksumType::Crc32c; // any type
    let mut buf = Vec::new();
    f.marshal(&mut buf).unwrap();
    // Corrupt magic
    buf[28..32].copy_from_slice(&0xDEAD_BEEFu32.to_le_bytes());
    PackedFileFooter::unmarshal(&buf).unwrap_err();
}

#[test]
fn test_has_newer_version() {
    // Test data: PK: [1, 1, 3, 7], Version: [3, 1, 100, 2]
    // This represents: (1,3), (1,1), (3,100), (7,2)
    let pk_data = [1u64, 1, 3, 7];
    let versions = [3u64, 1, 100, 2];

    let version_at = |i: usize| -> Result<u64> { Ok(versions[i]) };
    let pk_at = |i: usize| -> Result<u64> { Ok(pk_data[i]) };

    // PK=1, version=0, max_version=5 -> should find (1,v3) and (1,v1)
    assert!(has_newer_version(1u64, 0, 5, pk_data.len(), version_at, pk_at).unwrap());

    // PK=1, version=1, max_version=5 -> should find (1,v3)
    assert!(has_newer_version(1u64, 1, 5, pk_data.len(), version_at, pk_at).unwrap());

    // PK=1, version=2, max_version=5 -> should find (1,v3)
    assert!(has_newer_version(1u64, 2, 5, pk_data.len(), version_at, pk_at).unwrap());

    // PK=1, version=3, max_version=5 -> no version > 3
    assert!(!has_newer_version(1u64, 3, 5, pk_data.len(), version_at, pk_at).unwrap());

    // PK=1, version=10, max_version=150 -> no version > 10
    assert!(!has_newer_version(1u64, 10, 150, pk_data.len(), version_at, pk_at).unwrap());

    // PK=1, version=0, max_version=2 -> should find (1,v3)
    assert!(has_newer_version(1u64, 0, 2, pk_data.len(), version_at, pk_at).unwrap());

    // PK=2, not exists
    assert!(!has_newer_version(2u64, 0, 1000, pk_data.len(), version_at, pk_at).unwrap());

    // PK=3, version=50, max_version=150 -> should find (3,100)
    assert!(has_newer_version(3u64, 50, 150, pk_data.len(), version_at, pk_at).unwrap());

    // PK=3, version=50, max_version=100 -> should find (3,100)
    assert!(has_newer_version(3u64, 50, 100, pk_data.len(), version_at, pk_at).unwrap());

    // PK=3, version=1, max_version=50 -> (3,100) is newer than
    // 1 but exceeds max_version
    assert!(!has_newer_version(3u64, 1, 50, pk_data.len(), version_at, pk_at).unwrap());

    // PK=7, version=1, max_version=3 -> should find (7,2)
    assert!(has_newer_version(7u64, 1, 3, pk_data.len(), version_at, pk_at).unwrap());

    // PK=999, not exists
    assert!(!has_newer_version(999u64, 0, 1000, pk_data.len(), version_at, pk_at).unwrap());

    // Edge case: empty data
    assert!(!has_newer_version(1u64, 0, 1000, 0, version_at, pk_at).unwrap());

    // Edge case: exact version match with max_version
    assert!(!has_newer_version(1u64, 3, 3, pk_data.len(), version_at, pk_at).unwrap());
    assert!(has_newer_version(1u64, 2, 3, pk_data.len(), version_at, pk_at).unwrap());
}

#[tokio::test]
async fn test_int_pk() -> Result<()> {
    let mut buffer = Vec::new();
    let mut builder = PackedFileBuilder::new(
        Cursor::new(&mut buffer),
        PackedFileBuilderOptions::default(),
    );

    // Create test data with int PKs
    // First LP: lp1
    builder.start_lp(true, b"lp1")?;
    builder.add_pk_int(1, 100, false)?;
    builder.add_pk_int(2, 99, false)?;
    builder.add_pk_int(3, 98, true)?;
    builder.set_tantivy_index(b"tantivy_data_1")?;
    builder.finish_lp()?;

    // Second LP: lp2
    builder.start_lp(true, b"lp2")?;
    // Include an overlapping PK (1) that also exists in lp1 to ensure
    // LPs are independent even if PK bytes are the same.
    builder.add_pk_int(1, 60, false)?;
    builder.add_pk_int(10, 50, false)?;
    builder.add_pk_int(20, 49, false)?;
    builder.set_tantivy_index(b"tantivy_data_2")?;
    builder.finish_lp()?;

    builder.finish()?;

    let (_g, packed_file) = PackedFile::from_buffer(&buffer).await?;

    let lp1 = packed_file.get_logical_partition(b"lp1").await?.unwrap();
    assert_eq!(lp1.lp_key(), b"lp1");
    assert_eq!(lp1.n_keys(), 3);
    assert!(lp1.has_newer_version_int(1, 50, 150)?);
    assert!(!lp1.has_newer_version_int(1, 100, 150)?);
    assert!(lp1.has_newer_version_int(3, 10, 150)?);
    assert!(!lp1.has_newer_version_int(3, 98, 150)?);
    lp1.has_newer_version_common(&[], 0, 0).unwrap_err();

    let lp2 = packed_file.get_logical_partition(b"lp2").await?.unwrap();
    assert_eq!(lp2.lp_key(), b"lp2");
    assert_eq!(lp2.n_keys(), 3);
    assert!(lp2.has_newer_version_int(1, 50, 150)?);
    assert!(!lp2.has_newer_version_int(1, 60, 150)?);
    assert!(lp2.has_newer_version_int(10, 10, 150)?);
    assert!(!lp2.has_newer_version_int(10, 50, 150)?);
    lp2.has_newer_version_common(&[], 0, 0).unwrap_err();

    let missing = packed_file.get_logical_partition(b"lp_missing").await?;
    assert!(missing.is_none());
    assert_eq!(packed_file.n_data_blocks()?, 1);
    Ok(())
}

#[tokio::test]
async fn test_common_pk() -> Result<()> {
    let mut buffer = Vec::new();
    let mut builder = PackedFileBuilder::new(
        Cursor::new(&mut buffer),
        PackedFileBuilderOptions::default(),
    );

    // Create test data with common PKs
    builder.start_lp(false, b"lp_common1")?;
    builder.add_pk_common(b"pk_a", 200, false)?;
    builder.add_pk_common(b"pk_b", 199, false)?;
    builder.add_pk_common(b"pk_c", 198, true)?;
    builder.set_tantivy_index(b"tantivy_common_1")?;
    builder.finish_lp()?;

    builder.finish()?;

    let (_g, packed_file) = PackedFile::from_buffer(&buffer).await?;

    let lp = packed_file
        .get_logical_partition(b"lp_common1")
        .await?
        .unwrap();
    assert_eq!(lp.n_keys(), 3);
    assert!(lp.has_newer_version_common(b"pk_a", 150, 250)?);
    assert!(!lp.has_newer_version_common(b"pk_a", 200, 250)?);
    assert!(lp.has_newer_version_common(b"pk_b", 150, 250)?);
    assert!(lp.has_newer_version_common(b"pk_c", 150, 300)?);
    assert!(!lp.has_newer_version_common(b"pk_c", 198, 300)?);
    lp.has_newer_version_int(1, 0, 0).unwrap_err();
    assert_eq!(packed_file.n_data_blocks()?, 1);
    Ok(())
}

#[tokio::test]
async fn test_mixed_int_and_common_pk() -> Result<()> {
    // Build a packed file containing exactly two LPs: one int-PK LP and one
    // common-PK LP.
    let mut buffer = Vec::new();
    let mut builder = PackedFileBuilder::new(
        Cursor::new(&mut buffer),
        PackedFileBuilderOptions {
            block_size: 1024,
            checksum_type: ChecksumType::Crc32c,
        },
    );

    // Int PK LP: lp_int (will be added after lp_comm to respect ordering)
    // Common PK LP: lp_comm comes first lexicographically ("lp_comm" < "lp_int")
    builder.start_lp(false, b"lp_comm")?;
    builder.add_pk_common(b"a_key", 500, false)?;
    builder.add_pk_common(b"a_key", 450, false)?; // older same key
    builder.add_pk_common(b"b_key", 400, true)?;
    builder.add_pk_common(b"c_key", 350, false)?;
    builder.set_tantivy_index(b"idx_comm")?;
    builder.finish_lp()?;

    builder.start_lp(true, b"lp_int")?;
    builder.add_pk_int(10, 300, false)?; // Newest
    builder.add_pk_int(10, 250, false)?; // Older version same PK
    builder.add_pk_int(11, 200, true)?; // A delete mark PK
    builder.set_tantivy_index(b"idx_int")?;
    builder.finish_lp()?;

    builder.finish()?;

    let (_g, packed_file) = PackedFile::from_buffer(&buffer).await?;

    let lp_int = packed_file.get_logical_partition(b"lp_int").await?.unwrap();
    let lp_comm = packed_file
        .get_logical_partition(b"lp_comm")
        .await?
        .unwrap();
    assert_eq!(lp_int.lp_key(), b"lp_int");
    assert_eq!(lp_comm.lp_key(), b"lp_comm");
    assert_eq!(lp_int.n_keys(), 3); // 10x2 + 11
    assert_eq!(lp_comm.n_keys(), 4); // a_key x2 + b_key + c_key

    // Int PK checks
    // pk10 has versions 300 & 250
    assert!(lp_int.has_newer_version_int(10, 200, 400)?); // 300,250 >200
    assert!(lp_int.has_newer_version_int(10, 250, 400)?); // 300 >250
    assert!(!lp_int.has_newer_version_int(10, 300, 400)?); // none >300
    assert!(lp_int.has_newer_version_int(10, 200, 299)?); // 250 fits <=299 >200
    assert!(!lp_int.has_newer_version_int(10, 200, 249)?); // neither 300 nor 250 <=249
    // pk11 only one version 200
    assert!(lp_int.has_newer_version_int(11, 150, 400)?);
    assert!(!lp_int.has_newer_version_int(11, 200, 400)?);

    // Common PK checks
    assert!(lp_comm.has_newer_version_common(b"a_key", 100, 600)?); // 500,450
    assert!(lp_comm.has_newer_version_common(b"a_key", 450, 600)?); // 500
    assert!(!lp_comm.has_newer_version_common(b"a_key", 500, 600)?);
    assert!(lp_comm.has_newer_version_common(b"b_key", 300, 600)?); // 400
    assert!(!lp_comm.has_newer_version_common(b"b_key", 400, 600)?);
    assert!(lp_comm.has_newer_version_common(b"c_key", 300, 600)?); // 350
    assert!(!lp_comm.has_newer_version_common(b"c_key", 350, 600)?);

    // Cross-LP negative: keys should not appear in other LP
    let none = packed_file.get_logical_partition(b"nonexistent_lp").await?;
    assert!(none.is_none());
    assert_eq!(packed_file.n_data_blocks()?, 1);
    Ok(())
}

#[tokio::test]
async fn test_builder_multiple_data_blocks() -> Result<()> {
    // Force extremely small block size so each LP will reside in its own data
    // block.
    let options = PackedFileBuilderOptions {
        block_size: 1,
        checksum_type: ChecksumType::Crc32c,
    };

    let mut buffer = Vec::new();
    let mut builder = PackedFileBuilder::new(Cursor::new(&mut buffer), options);

    // LP keys must be in ascending order: lp_a < lp_b < lp_c
    // LP 1: lp_a (int PKs)
    builder.start_lp(true, b"lp_a")?;
    builder.add_pk_int(1, 300, false)?; // Single version
    builder.add_pk_int(2, 250, true)?; // Delete mark
    builder.set_tantivy_index(b"tantivy_lp_a")?;
    builder.finish_lp()?;

    // LP 2: lp_b
    builder.start_lp(true, b"lp_b")?;
    builder.add_pk_int(10, 200, false)?;
    builder.add_pk_int(20, 190, false)?;
    builder.add_pk_int(30, 180, true)?;
    builder.set_tantivy_index(b"tantivy_lp_b")?;
    builder.finish_lp()?;

    // LP 3: lp_c with duplicate PK versions
    builder.start_lp(true, b"lp_c")?;
    builder.add_pk_int(100, 160, false)?; // Newest
    builder.add_pk_int(100, 150, false)?; // Older version same PK
    builder.add_pk_int(110, 140, true)?;
    builder.set_tantivy_index(b"tantivy_lp_c")?;
    builder.finish_lp()?;

    builder.finish()?;

    let (_g, packed_file) = PackedFile::from_buffer(&buffer).await?;

    // lp_a validations
    let lp_a = packed_file.get_logical_partition(b"lp_a").await?.unwrap();
    assert_eq!(lp_a.n_keys(), 2);
    assert!(lp_a.has_newer_version_int(1, 100, 400)?);
    assert!(!lp_a.has_newer_version_int(1, 300, 400)?);
    assert!(lp_a.has_newer_version_int(2, 0, 400)?); // delete-mark version still counts
    assert!(!lp_a.has_newer_version_int(2, 250, 400)?);

    // lp_b validations
    let lp_b = packed_file.get_logical_partition(b"lp_b").await?.unwrap();
    assert_eq!(lp_b.n_keys(), 3);
    assert!(lp_b.has_newer_version_int(10, 150, 400)?);
    assert!(!lp_b.has_newer_version_int(10, 200, 400)?);
    assert!(lp_b.has_newer_version_int(30, 100, 400)?);
    assert!(!lp_b.has_newer_version_int(30, 180, 400)?);

    // lp_c validations
    let lp_c = packed_file.get_logical_partition(b"lp_c").await?.unwrap();
    assert_eq!(lp_c.n_keys(), 3);
    assert!(lp_c.has_newer_version_int(100, 120, 400)?); // 160,150 >120
    assert!(lp_c.has_newer_version_int(100, 150, 400)?); // 160 >150
    assert!(!lp_c.has_newer_version_int(100, 160, 400)?); // none >160
    assert!(lp_c.has_newer_version_int(110, 100, 400)?); // 140
    assert!(!lp_c.has_newer_version_int(110, 140, 400)?);

    // Negative cases
    let none = packed_file.get_logical_partition(b"lp_d").await?;
    assert!(none.is_none());
    assert_eq!(packed_file.n_data_blocks()?, 3);
    Ok(())
}

#[tokio::test]
async fn test_empty_file() -> Result<()> {
    let options = PackedFileBuilderOptions::default();

    let mut buffer = Vec::new();
    let builder = PackedFileBuilder::new(Cursor::new(&mut buffer), options);
    builder.finish()?;

    let (_g, packed_file) = PackedFile::from_buffer(&buffer).await?;
    assert!(
        packed_file
            .get_logical_partition(b"anything")
            .await?
            .is_none()
    );
    assert_eq!(packed_file.n_data_blocks()?, 0);
    Ok(())
}

#[tokio::test]
async fn test_roundtrip_multiple_lps_with_tantivy_data() -> Result<()> {
    let options = PackedFileBuilderOptions {
        block_size: 512, // Smaller block size to test multiple blocks
        checksum_type: ChecksumType::Crc32c,
    };

    let mut buffer = Vec::new();
    let mut builder = PackedFileBuilder::new(Cursor::new(&mut buffer), options);

    // Create multiple LPs with different tantivy data
    builder.start_lp(true, b"lp1")?;
    builder.add_pk_int(1, 100, false)?;
    builder.add_pk_int(2, 99, false)?;
    builder.set_tantivy_index(b"tantivy_data_for_lp1")?;
    builder.finish_lp()?;

    builder.start_lp(false, b"lp2")?;
    builder.add_pk_common(b"key_a", 200, false)?;
    builder.add_pk_common(b"key_b", 199, true)?;
    builder.set_tantivy_index(b"tantivy_data_for_lp2")?;
    builder.finish_lp()?;

    builder.start_lp(true, b"lp3")?;
    builder.add_pk_int(10, 300, false)?;
    builder.add_pk_int(20, 299, false)?;
    builder.add_pk_int(30, 298, true)?;
    builder.set_tantivy_index(b"tantivy_data_for_lp3")?;
    builder.finish_lp()?;

    builder.finish()?;

    let (_g, packed_file) = PackedFile::from_buffer(&buffer).await?;

    let lp1 = packed_file.get_logical_partition(b"lp1").await?.unwrap();
    assert_eq!(lp1.lp_key(), b"lp1");
    assert_eq!(lp1.n_keys(), 2);
    let lp2 = packed_file.get_logical_partition(b"lp2").await?.unwrap();
    assert_eq!(lp2.lp_key(), b"lp2");
    assert_eq!(lp2.n_keys(), 2);
    let lp3 = packed_file.get_logical_partition(b"lp3").await?.unwrap();
    assert_eq!(lp3.lp_key(), b"lp3");
    assert_eq!(lp3.n_keys(), 3);
    assert!(lp1.has_newer_version_int(1, 50, 150)?);
    assert!(!lp1.has_newer_version_int(1, 100, 150)?);
    assert!(lp2.has_newer_version_common(b"key_a", 150, 250)?);
    assert!(!lp2.has_newer_version_common(b"key_a", 200, 250)?);
    assert!(lp3.has_newer_version_int(30, 250, 350)?);
    assert!(!lp3.has_newer_version_int(30, 298, 350)?);
    let lp_none = packed_file.get_logical_partition(b"lp_nonexistent").await?;
    let n_blocks = packed_file.n_data_blocks()?;
    assert!(n_blocks >= 1);
    assert!(lp_none.is_none());
    Ok(())
}

#[tokio::test]
async fn test_checksum_data_block_corruption() -> Result<()> {
    // Build a simple file with checksum enabled.
    let options = PackedFileBuilderOptions {
        block_size: 1024,
        checksum_type: ChecksumType::Crc32c,
    };
    let mut buffer = Vec::new();
    let mut builder = PackedFileBuilder::new(Cursor::new(&mut buffer), options);

    builder.start_lp(true, b"lp1")?;
    builder.add_pk_int(1, 100, false)?;
    builder.finish_lp()?;
    builder.finish()?;

    // Corrupt a byte in the data block
    buffer[5] ^= 0xFF;

    // Open and read should fail with data block checksum mismatch.
    let (_g, packed_file) = PackedFile::from_buffer(&buffer).await?;

    let err = packed_file
        .get_logical_partition(b"lp1")
        .await
        .err()
        .unwrap()
        .to_string();
    assert!(err.contains("data block checksum mismatch"));

    Ok(())
}

#[tokio::test]
async fn test_get_lp_edge_cases() -> Result<()> {
    let mut buffer = Vec::new();
    let mut builder = PackedFileBuilder::new(
        Cursor::new(&mut buffer),
        PackedFileBuilderOptions::default(),
    );

    builder.start_lp(true, b"lp_b")?;
    builder.add_pk_int(1, 100, false)?;
    builder.finish_lp()?;

    builder.start_lp(true, b"lp_d")?;
    builder.add_pk_int(2, 200, false)?;
    builder.finish_lp()?;

    builder.finish()?;

    let (_g, packed_file) = PackedFile::from_buffer(&buffer).await?;

    // Key before the first LP
    let lp_a = packed_file.get_logical_partition(b"lp_a").await?;
    assert!(lp_a.is_none());

    // Key that should exist
    let lp_b = packed_file.get_logical_partition(b"lp_b").await?;
    assert!(lp_b.is_some());

    // Key between two existing LPs
    let lp_c = packed_file.get_logical_partition(b"lp_c").await?;
    assert!(lp_c.is_none());

    // Key that should exist
    let lp_d = packed_file.get_logical_partition(b"lp_d").await?;
    assert!(lp_d.is_some());

    // Key after the last LP
    let lp_e = packed_file.get_logical_partition(b"lp_e").await?;
    assert!(lp_e.is_none());

    Ok(())
}

// TODO: Check smallest LP key and largest LP key
